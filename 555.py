import torch


def test_pytorch_environment():
    # 检查PyTorch版本和CUDA是否可用
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA是否可用: {'可用' if torch.cuda.is_available() else '不可用'}")

    # 如果CUDA可用，显示GPU信息
    if torch.cuda.is_available():
        print(f"当前GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU设备数量: {torch.cuda.device_count()}")

    # 创建一个简单的张量并执行计算
    print("\n测试张量计算...")
    a = torch.tensor([1., 2., 3.])
    b = torch.tensor([4., 5., 6.])
    c = a + b
    print(f"a + b = {c}")

    # 测试矩阵乘法
    print("\n测试矩阵乘法...")
    mat1 = torch.randn(2, 3)
    mat2 = torch.randn(3, 2)
    mat3 = torch.matmul(mat1, mat2)
    print(f"矩阵乘法结果:\n{mat3}")

    # 测试自动梯度计算
    print("\n测试自动梯度...")
    x = torch.tensor(2., requires_grad=True)
    y = x ** 2 + 3 * x + 1
    y.backward()
    print(f"在x=2时，函数y=x^2+3x+1的导数是: {x.grad.item()}")


if __name__ == "__main__":
    test_pytorch_environment()