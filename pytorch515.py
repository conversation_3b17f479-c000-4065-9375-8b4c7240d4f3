import torch
import torch.nn as nn


# 定义一个简单的全连接神经网络
class SimpleNN(nn.Module):
    def __init__(self):
        super().__init__()
        self.layers = nn.Sequential(
            nn.Linear(784, 128),  # 输入层（假设输入是 784 维，如 MNIST 图片）
            nn.ReLU(),
            nn.Linear(128, 10)  # 输出层（10 分类）
        )

    def forward(self, x):
        return self.layers(x)


# 初始化模型、损失函数和优化器
model = SimpleNN()
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

# 模拟训练过程（实际需加载数据）
for epoch in range(10):
    inputs = torch.randn(32, 784)  # 假设输入数据（32 个样本）
    labels = torch.randint(0, 10, (32,))

    outputs = model(inputs)
    loss = criterion(outputs, labels)

    optimizer.zero_grad()
    loss.backward()
    optimizer.step()

    print(f"Epoch {epoch}, Loss: {loss.item():.4f}")