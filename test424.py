import os
import re
import numpy as np
from scipy.spatial.distance import euclidean
from scipy.stats import pearsonr
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from scipy.signal import find_peaks
# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def plot_all_curves(file_dict, ref_file=None):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())

    # 生成不同颜色
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    # 绘制所有曲线
    for idx, filename in enumerate(filenames):
        data = file_dict[filename]
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    # 设置图表属性
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能调整图例位置
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    plt.show()


def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前5个点
    if len(data) > 5:
        data = data[5:]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    return data

def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}

    # 遍历文件夹中的所有asc文件
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            data = process_otdr_file(file_path)
            if len(data) > 0:
                file_dict[filename] = data

    return file_dict


def enhanced_compare(data1, data2, threshold=0.85):
    """改进版相似度比对（三阶段验证）"""

    # 阶段1：DTW距离计算（改进归一化方式）
    def robust_normalize(arr):
        arr = np.array(arr)
        median = np.median(arr)
        mad = np.median(np.abs(arr - median))  # 使用中位数绝对偏差
        return (arr - median) / (mad + 1e-9)  # 避免除以零

    y1 = robust_normalize(data1[:, 1])
    y2 = robust_normalize(data2[:, 1])

    # 调整DTW参数（增加搜索半径）
    dtw_dist, _ = fastdtw(
        y1.reshape(-1, 1),
        y2.reshape(-1, 1),
        radius=len(y1) // 10,  # 动态半径
        dist=euclidean
    )
    dtw_sim = 1 / (1 + dtw_dist / len(y1))  # 长度归一化

    # 阶段2：关键事件点匹配（改进峰值检测）
    def find_key_events(y, prominence=0.5):
        peaks, _ = find_peaks(y, prominence=prominence)
        valleys, _ = find_peaks(-y, prominence=prominence)
        return sorted(set(peaks.tolist() + valleys.tolist()))

    events1 = find_key_events(y1)
    events2 = find_key_events(y2)

    # 事件点相似度（考虑位置容差）
    match_count = 0
    for e1 in events1[:5]:  # 只比较前5个主要事件
        for e2 in events2[:5]:
            if abs(e1 - e2) <= len(y1) // 20:  # 5%位置容差
                match_count += 1
                break
    event_sim = match_count / max(len(events1[:5]), len(events2[:5]), 1)

    # 阶段3：曲线趋势验证（分段斜率比较）
    def calc_trend_similarity(y1, y2, segments=5):
        seg_len = len(y1) // segments
        similarities = []
        for i in range(segments):
            seg1 = y1[i * seg_len: (i + 1) * seg_len]
            seg2 = y2[i * seg_len: (i + 1) * seg_len]
            if len(seg1) > 1 and len(seg2) > 1:
                slope1 = np.polyfit(range(len(seg1)), seg1, 1)[0]
                slope2 = np.polyfit(range(len(seg2)), seg2, 1)[0]
                similarities.append(1 - abs(slope1 - slope2))
        return np.mean(similarities) if similarities else 0

    trend_sim = calc_trend_similarity(y1, y2)

    # 综合评分（加权平均）
    composite_sim = 0.6 * dtw_sim + 0.1 * event_sim + 0.3 * trend_sim

    # 动态阈值调整（基于曲线复杂度）
    complexity = len(events1) + len(events2)
    adjusted_threshold = threshold * (1 + complexity / 20)  # 复杂曲线提高阈值

    is_same = composite_sim > threshold
    return {
        'is_same': is_same,
        'composite_sim': composite_sim,
        'dtw_sim': dtw_sim,
        'event_sim': event_sim,
        'trend_sim': trend_sim,
        'adjusted_threshold': adjusted_threshold
    }


def compare_files(file_dict, threshold=0.8):
    """改进后的文件比对"""
    filenames = list(file_dict.keys())
    results = []

    for i in range(len(filenames)):
        for j in range(i + 1, len(filenames)):
            file1 = filenames[i]
            file2 = filenames[j]

            res = enhanced_compare(file_dict[file1], file_dict[file2], threshold)

            result_str = (
                f"{file1} vs {file2}:\n"
                f"  综合相似度: {res['composite_sim']:.3f} (DTW: {res['dtw_sim']:.3f}, "
                f"事件: {res['event_sim']:.3f}, 趋势: {res['trend_sim']:.3f})\n"
                f"  调整阈值: {res['adjusted_threshold']:.3f} → "
                f"判定结果: {'同缆' if res['is_same'] else '不同缆'}\n"
                f"{'-' * 60}"
            )
            results.append(result_str)

    # 按相似度排序输出
    return sorted(results, key=lambda x: float(re.search(r"综合相似度: (\d+\.\d+)", x).group(1)), reverse=True)


if __name__ == "__main__":
    folder_path = "C:/Users/<USER>/Desktop/co-cable/co-cable"
    file_data = process_folder(folder_path)

    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        plot_all_curves(file_data)
        print("\n同缆检测结果（按相似度排序）：")
        print("=" * 65)
        for result in compare_files(file_data):
            print(result)
        print("=" * 65)
        print(f"共完成 {len(file_data)} 个文件的 {len(file_data) * (len(file_data) - 1) // 2} 次比对")