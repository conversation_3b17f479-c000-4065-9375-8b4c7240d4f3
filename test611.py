import os
os.environ['KMP_DUPLICATE_LIB_OK']='True'  # 解决OpenMP冲突问题

import re
import numpy as np
from scipy.spatial.distance import euclidean, cosine
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from itertools import combinations
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim
import time  # 添加时间模块导入

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class OTDRDataset(Dataset):
    def __init__(self, curves_pairs, labels):  
        self.curves_pairs = curves_pairs  # 每个元素是一对OTDR曲线数据
        self.labels = labels  # 1表示同缆，0表示不同缆
        
    def __len__(self):
        return len(self.labels)     
    
    def __getitem__(self, idx): # 获取数据集中的第idx个样本
        curve1, curve2 = self.curves_pairs[idx]
        # 将数据转换为张量，并保持2D形状 (长度, 特征数=2)
        curve1_tensor = torch.FloatTensor(curve1)
        curve2_tensor = torch.FloatTensor(curve2)
        label = torch.FloatTensor([self.labels[idx]])
        return curve1_tensor, curve2_tensor, label

class OTDRNet(nn.Module):
    def __init__(self, input_size=2, hidden_size=128):
        super(OTDRNet, self).__init__()
        self.hidden_size = hidden_size
        
        # 特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_size, 32),
            nn.ReLU(),  
            nn.Linear(32, 64),
            nn.ReLU(),
        )
        
        # LSTM层处理不同长度的序列
        self.lstm = nn.LSTM(input_size=64, 
                           hidden_size=hidden_size,
                           num_layers=2,
                           batch_first=True,
                           bidirectional=True,
                           dropout=0.2)
        
        # 注意力层
        self.attention = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1)
        )
        
        # 相似度权重注意力层
        self.similarity_attention = nn.Sequential(
            nn.Linear(3, 16),  # 3个相似度指标
            nn.ReLU(),
            nn.Linear(16, 3),
            nn.Softmax(dim=1)
        )
        
        # 特征融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_size * 4 + 3, hidden_size * 2),  # 增加3个相似度特征
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
        )
        
        # 相似度计算层
        self.similarity = nn.Sequential(
            nn.Linear(hidden_size, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def attention_net(self, lstm_output):
        attn_weights = self.attention(lstm_output)
        attn_weights = F.softmax(attn_weights, dim=1)
        context = torch.bmm(lstm_output.transpose(1, 2), attn_weights)
        return context.squeeze(2)
    
    def compute_cosine_similarity(self, x1, x2):
        # 计算余弦相似度
        x1_norm = F.normalize(x1, p=2, dim=1)
        x2_norm = F.normalize(x2, p=2, dim=1)
        return torch.sum(x1_norm * x2_norm, dim=1, keepdim=True)
    
    def compute_euclidean_distance(self, x1, x2):
        # 计算欧几里得距离并归一化
        dist = torch.norm(x1 - x2, p=2, dim=1, keepdim=True)
        return torch.exp(-dist)  # 转换为相似度度量
    
    def compute_dtw_similarity(self, x1, x2):
        # 在GPU上计算DTW相似度
        batch_size = x1.size(0)
        device = x1.device
        dtw_sims = torch.zeros(batch_size, 1).to(device)
        
        for i in range(batch_size):
            # 将张量转换为numpy数组进行DTW计算
            x1_np = x1[i].detach().cpu().numpy()
            x2_np = x2[i].detach().cpu().numpy()
            distance, _ = fastdtw(x1_np, x2_np, dist=euclidean)
            # 将DTW距离转换为相似度并归一化
            similarity = torch.tensor([[np.exp(-distance / 1000)]], dtype=torch.float32).to(device)
            dtw_sims[i] = similarity
            
        return dtw_sims
        
    def forward_single(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 对每个时间步进行特征提取
        x = x.reshape(-1, x.size(-1))
        x = self.feature_extractor(x)
        x = x.reshape(batch_size, seq_len, -1)
        
        # LSTM处理序列
        lstm_out, _ = self.lstm(x)
        
        # 使用注意力机制
        attn_out = self.attention_net(lstm_out)
        
        # 全局平均池化和最大池化
        avg_pool = torch.mean(lstm_out, dim=1)
        max_pool, _ = torch.max(lstm_out, dim=1)
        
        # 合并特征
        pooled = torch.cat([attn_out, avg_pool], dim=1)
        return pooled, lstm_out
    
    def forward(self, x1, x2):
        # 分别处理两条曲线
        feat1, lstm_out1 = self.forward_single(x1)
        feat2, lstm_out2 = self.forward_single(x2)
        
        # 计算三种相似度
        cs = self.compute_cosine_similarity(feat1, feat2)
        ed = self.compute_euclidean_distance(feat1, feat2)
        dtw = self.compute_dtw_similarity(lstm_out1, lstm_out2)
        
        # 将三种相似度合并
        similarities = torch.cat([cs, ed, dtw], dim=1)
        
        # 使用注意力机制计算相似度权重
        similarity_weights = self.similarity_attention(similarities)
        weighted_similarities = similarities * similarity_weights
        
        # 计算特征差异
        diff = torch.abs(feat1 - feat2)
        
        # 特征融合（包含加权相似度）
        fused = self.fusion(torch.cat([diff, weighted_similarities], dim=1))
        
        # 计算最终相似度
        similarity = self.similarity(fused)
        return similarity

def collate_fn(batch):
    """自定义批处理函数，处理不同长度的序列"""
    curves1, curves2, labels = zip(*batch)
    
    # 转换为张量
    labels = torch.stack(labels)
    
    # 直接返回列表，不进行填充
    return curves1, curves2, labels

def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    try:
        # 首先尝试 utf-8 编码
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            # 如果 utf-8 失败，尝试 gbk 编码
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                # 如果 gbk 也失败，尝试 latin-1 编码
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
            except Exception as e:
                print(f"无法读取文件 {file_path}: {str(e)}")
                return np.array([])

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        try:
            pos, val = map(float, line.strip().split())
            data.append([pos, val])
        except ValueError:
            continue  # 跳过无法转换为数值的行

    data = np.array(data)

    # 如果没有有效数据，返回空数组
    if len(data) == 0:
        print(f"警告：文件 {file_path} 中没有找到有效数据")
        return np.array([])

    # 去除首端前10个点和尾端40个点
    if len(data) > 50:
        data = data[10:-40]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    return data

def process_folder(folder_path):
    """递归处理文件夹及其子文件夹中的OTDR文件"""
    file_dict = {}
    folder_index = {}  # 用于存储文件夹索引

    def get_file_number(filename):
        """从文件名中提取数字"""
        # 移除扩展名
        name = os.path.splitext(filename)[0]
        # 提取数字部分
        numbers = ''.join(c for c in name if c.isdigit())
        return int(numbers) if numbers else 0

    def process_subfolder(current_path, relative_path=''):
        # 只获取.asc文件
        files = [f for f in os.listdir(current_path) if f.endswith(".asc")]
        
        # 如果当前文件夹包含.asc文件，将其作为一个独立的文件夹处理
        if files:
            current_folder = os.path.basename(current_path)
            if current_folder not in folder_index:
                folder_index[current_folder] = len(folder_index) + 1
            
            folder_idx = folder_index[current_folder]
            
            # 处理当前文件夹中的所有.asc文件
            for filename in files:
                file_path = os.path.join(current_path, filename)
                data = process_otdr_file(file_path)
                
                if len(data) > 0:
                    # 使用文件名中的数字作为文件编号
                    file_number = get_file_number(filename)
                    simple_id = f"{folder_idx}-{file_number}"
                    file_dict[simple_id] = {
                        'data': data,
                        'original_name': filename,
                        'full_path': file_path,
                        'folder_name': current_folder
                    }
        
        # 处理子文件夹
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)
            if os.path.isdir(item_path):
                new_relative_path = os.path.join(relative_path, item)
                process_subfolder(item_path, new_relative_path)

    # 开始递归处理
    process_subfolder(folder_path)
    
    if not file_dict:
        print("\n警告：未找到任何.asc文件！")
        return file_dict
    
    # 打印文件夹映射信息
    print("\n文件夹编号对应关系：")
    for folder, idx in sorted(folder_index.items(), key=lambda x: x[1]):
        print(f"文件夹 {idx}: {folder}")
        
    # 打印找到的文件
    print("\n找到的.asc文件：")
    for file_id, file_info in sorted(file_dict.items()):
        print(f"{file_id}: {file_info['original_name']}")
    
    return file_dict

def plot_all_curves(file_dict, ref_file=None, save_path='otdr_curves.png'):
    """绘制所有曲线在同一图中并保存为图片文件"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())

    # 生成不同颜色
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    # 绘制所有曲线
    for idx, filename in enumerate(filenames):
        data = file_dict[filename]['data']  # 修改这里以适应新的数据结构
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    # 设置图表属性
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能调整图例位置
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    
    # 保存图片到文件
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形，释放内存
    print(f"图形已保存至: {save_path}")

def compute_cosine_similarity(curve1, curve2):
    """计算余弦相似度"""
    # 确保两条曲线长度相同（通过插值实现）
    target_length = min(len(curve1), len(curve2))
    if len(curve1) != target_length:
        x1 = np.linspace(0, 1, len(curve1))
        x_new = np.linspace(0, 1, target_length)
        curve1 = np.column_stack((
            np.interp(x_new, x1, curve1[:, 0]),
            np.interp(x_new, x1, curve1[:, 1])
        ))
    if len(curve2) != target_length:
        x2 = np.linspace(0, 1, len(curve2))
        x_new = np.linspace(0, 1, target_length)
        curve2 = np.column_stack((
            np.interp(x_new, x2, curve2[:, 0]),
            np.interp(x_new, x2, curve2[:, 1])
        ))
    
    # 计算余弦相似度
    similarity = 1 - cosine(curve1.flatten(), curve2.flatten())
    return similarity

def compute_euclidean_similarity(curve1, curve2):
    """计算基于欧几里得距离的相似度"""
    # 确保两条曲线长度相同（通过插值实现）
    target_length = min(len(curve1), len(curve2))
    if len(curve1) != target_length:
        x1 = np.linspace(0, 1, len(curve1))
        x_new = np.linspace(0, 1, target_length)
        curve1 = np.column_stack((
            np.interp(x_new, x1, curve1[:, 0]),
            np.interp(x_new, x1, curve1[:, 1])
        ))
    if len(curve2) != target_length:
        x2 = np.linspace(0, 1, len(curve2))
        x_new = np.linspace(0, 1, target_length)
        curve2 = np.column_stack((
            np.interp(x_new, x2, curve2[:, 0]),
            np.interp(x_new, x2, curve2[:, 1])
        ))
    
    # 计算欧几里得距离并转换为相似度
    distance = np.linalg.norm(curve1.flatten() - curve2.flatten())
    similarity = np.exp(-distance / 1000)  # 使用指数函数将距离转换为相似度
    return similarity

def compute_dtw_similarity(curve1, curve2):
    """计算基于DTW的相似度"""
    distance, _ = fastdtw(curve1, curve2, dist=euclidean)
    similarity = np.exp(-distance / 1000)  # 使用指数函数将距离转换为相似度
    return similarity

def ensemble_similarity(curve1, curve2, weights=None):
    """集成三种相似度算法"""
    if weights is None:
        weights = [0.3, 0.3, 0.4]  # 默认权重：CS=0.3, ED=0.3, DTW=0.4
    
    # 计算三种相似度
    cs = compute_cosine_similarity(curve1, curve2)
    ed = compute_euclidean_similarity(curve1, curve2)
    dtw = compute_dtw_similarity(curve1, curve2)
    
    # 加权平均
    similarity = weights[0] * cs + weights[1] * ed + weights[2] * dtw
    
    return similarity, {
        'cosine': cs,
        'euclidean': ed,
        'dtw': dtw
    }

def analyze_curves(file_dict, similarity_threshold=0.7, weights=None):
    """分析所有曲线对的相似度并进行分组"""
    results = []
    
    # 计算所有曲线对的相似度
    for (file1, file2) in combinations(file_dict.keys(), 2):
        data1 = file_dict[file1]['data']
        data2 = file_dict[file2]['data']
        
        similarity, details = ensemble_similarity(data1, data2, weights)
        
        results.append({
            'file1': file1,
            'file2': file2,
            'similarity': similarity,
            'details': details,
            'is_same_cable': similarity >= similarity_threshold
        })
    
    # 根据相似度进行分组
    groups = []
    used_files = set()
    
    # 首先处理相似度高的对
    results.sort(key=lambda x: x['similarity'], reverse=True)
    
    for result in results:
        if result['similarity'] >= similarity_threshold:
            file1, file2 = result['file1'], result['file2']
            
            # 查找现有组
            matching_groups = []
            for group in groups:
                if file1 in group or file2 in group:
                    matching_groups.append(group)
            
            if not matching_groups:
                # 如果没有匹配的组，创建新组
                groups.append({file1, file2})
            else:
                # 合并所有匹配的组
                new_group = set()
                for group in matching_groups:
                    new_group.update(group)
                    groups.remove(group)
                new_group.add(file1)
                new_group.add(file2)
                groups.append(new_group)
            
            used_files.update([file1, file2])
    
    # 将未分组的文件单独成组
    for file in file_dict.keys():
        if file not in used_files:
            groups.append({file})
    
    return results, groups

def plot_similarity_matrix(results, file_dict):
    """绘制相似度矩阵热图"""
    files = sorted(list(file_dict.keys()))
    n = len(files)
    similarity_matrix = np.zeros((n, n))
    
    # 构建文件索引映射
    file_to_idx = {file: i for i, file in enumerate(files)}
    
    # 填充相似度矩阵
    for result in results:
        i = file_to_idx[result['file1']]
        j = file_to_idx[result['file2']]
        similarity_matrix[i, j] = result['similarity']
        similarity_matrix[j, i] = result['similarity']  # 对称矩阵
    
    # 对角线设为1
    np.fill_diagonal(similarity_matrix, 1)
    
    # 绘制热图
    plt.figure(figsize=(12, 10))
    plt.imshow(similarity_matrix, cmap='YlOrRd', interpolation='nearest')
    plt.colorbar(label='相似度')
    
    # 设置刻度标签
    plt.xticks(range(n), files, rotation=45, ha='right')
    plt.yticks(range(n), files)
    
    plt.title('OTDR曲线相似度矩阵')
    plt.tight_layout()
    plt.savefig('similarity_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_analysis_report(file_dict, results, groups, output_file='ensemble_analysis_report.txt'):
    """生成分析报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("OTDR曲线集成分析报告\n")
        f.write("=" * 80 + "\n\n")
        
        # 写入分析参数
        f.write("1. 分析参数\n")
        f.write("-" * 40 + "\n")
        f.write("使用的相似度算法：余弦相似度(CS)、欧几里得距离(ED)、动态时间规整(DTW)\n")
        f.write("算法权重：CS=0.3, ED=0.3, DTW=0.4\n\n")
        
        # 写入文件统计信息
        f.write("2. 文件统计信息\n")
        f.write("-" * 40 + "\n")
        f.write(f"总计分析文件数: {len(file_dict)}\n")
        f.write(f"识别出的同缆组数: {len(groups)}\n\n")
        
        # 写入分组结果
        f.write("3. 同缆分组结果\n")
        f.write("-" * 40 + "\n")
        for i, group in enumerate(groups, 1):
            f.write(f"\n分组 {i}:\n")
            for file in sorted(group):
                f.write(f"  {file}\n")
        
        # 写入详细相似度分析
        f.write("\n4. 详细相似度分析\n")
        f.write("-" * 40 + "\n")
        for result in sorted(results, key=lambda x: x['similarity'], reverse=True):
            f.write(f"\n{result['file1']} vs {result['file2']}:\n")
            f.write(f"  总体相似度: {result['similarity']:.4f}\n")
            f.write(f"  余弦相似度: {result['details']['cosine']:.4f}\n")
            f.write(f"  欧氏相似度: {result['details']['euclidean']:.4f}\n")
            f.write(f"  DTW相似度: {result['details']['dtw']:.4f}\n")
            f.write(f"  判定结果: {'同缆' if result['is_same_cable'] else '非同缆'}\n")
        
        # 写入时间戳
        f.write(f"\n\n报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n")
    
    print(f"\n分析报告已保存至: {output_file}")

if __name__ == "__main__":
    # 设置文件夹路径
    folder_path = input("C:/Users/<USER>/Desktop/干线测试")  
    
    # 处理文件
    file_data = process_folder(folder_path)
    
    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        print(f"\n找到 {len(file_data)} 个有效的OTDR文件")
        
        # 设置相似度阈值和权重
        similarity_threshold = float(input("\n请输入相似度阈值(0-1之间，建议0.7): ") or "0.7")
        weights = [0.3, 0.3, 0.4]  # 可以根据需要调整权重
        
        # 分析曲线
        print("\n正在分析曲线...")
        results, groups = analyze_curves(file_data, similarity_threshold, weights)
        
        # 绘制相似度矩阵
        print("正在生成相似度矩阵...")
        plot_similarity_matrix(results, file_data)
        
        # 生成分析报告
        print("正在生成分析报告...")
        generate_analysis_report(file_data, results, groups)
        
        # 绘制所有曲线对比图
        print("正在生成曲线对比图...")
        plot_all_curves(file_data, save_path='otdr_curves_comparison.png')
        
        print("\n分析完成！") 