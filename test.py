import os
import re
import numpy as np
from scipy.spatial.distance import euclidean
from scipy.stats import pearsonr
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def plot_all_curves(file_dict, ref_file=None):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())

    # 生成不同颜色
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    # 绘制所有曲线
    for idx, filename in enumerate(filenames):
        data = file_dict[filename]
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    # 设置图表属性
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能调整图例位置
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    plt.show()
    plt.draw()  # 更新图形但不阻塞

def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前5个点
    if len(data) > 5:
        data = data[5:]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    return data


def enhanced_align(data1, data2, num_points=500):
    """500点全范围动态对齐函数"""
    # 获取全局范围
    min_pos = min(data1[0, 0], data2[0, 0])
    max_pos = max(data1[-1, 0], data2[-1, 0])
    
    # 生成插值坐标（固定500个点）
    common_pos = np.linspace(min_pos, max_pos, num_points)
    
    # 带边界值填充的线性插值
    aligned1 = np.interp(common_pos, 
                        data1[:, 0], data1[:, 1],
                        left=data1[0, 1], 
                        right=data1[-1, 1])
    
    aligned2 = np.interp(common_pos,
                        data2[:, 0], data2[:, 1],
                        left=data2[0, 1],
                        right=data2[-1, 1])
    
    return aligned1, aligned2

def enhanced_similarity(aligned1, aligned2):
    """基于500点欧氏距离的相似度计算"""
    # 标准化处理（Z-Score）
    def zscore_normalize(arr):
        return (arr - np.mean(arr)) / (np.std(arr) + 1e-9)
    
    norm1 = zscore_normalize(aligned1)
    norm2 = zscore_normalize(aligned2)
    
    # 计算欧氏距离
    raw_distance = euclidean(norm1, norm2)
    
    # 计算理论最大距离（假设数据在±3σ范围内）
    max_possible_dist = np.sqrt(len(norm1) * (6**2))  # 500*(6^2)=18000, sqrt=134.16
    
    # 将距离转换为相似度（0-1范围）
    similarity = 1 - (raw_distance / max_possible_dist)
    return max(0.0, similarity)

def enhanced_compare(data1, data2):
    """改进的比对流程"""
    try:
        aligned1, aligned2 = enhanced_align(data1, data2)
        if len(aligned1) != 500 or len(aligned2) != 500:
            return 0.0
        return enhanced_similarity(aligned1, aligned2)
    except Exception as e:
        print(f"比对异常: {str(e)}")
        return 0.0

def compare_files(file_dict, threshold=0.9):
    """改进的文件比对"""
    filenames = list(file_dict.keys())
    results = []
    
    for i in range(len(filenames)):
        for j in range(i+1, len(filenames)):
            sim_score = enhanced_compare(file_dict[filenames[i]],
                                        file_dict[filenames[j]])
            conclusion = "同缆" if sim_score > threshold else "不同缆"
            results.append(
                f"{filenames[i]} vs {filenames[j]} | "
                f"相似度: {sim_score:.2f} | 结论: {conclusion}"
            )
    return results

def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}

    # 遍历文件夹中的所有asc文件
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            data = process_otdr_file(file_path)
            if len(data) > 0:
                file_dict[filename] = data

    return file_dict




if __name__ == "__main__":
    folder_path = "C:/Users/<USER>/Desktop/co-cable/co-cable"
    file_data = process_folder(folder_path)
    plot_all_curves(file_data)
    
    print("\n同缆检测结果：")
    print("=" * 65)
    for result in compare_files(file_data):
        print(result)
    print("=" * 65)
    print(f"共完成 {len(file_data)} 个文件的比对")