import os
import re
import numpy as np
from scipy.spatial.distance import euclidean
from scipy.stats import pearsonr
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def plot_all_curves(file_dict, ref_file=None):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())

    # 生成不同颜色
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    # 绘制所有曲线
    for idx, filename in enumerate(filenames):
        data = file_dict[filename]
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    # 设置图表属性
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能调整图例位置
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    plt.show()


def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前5个点
    if len(data) > 5:
        data = data[5:]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    return data


def dtw_align_and_compare(data1, data2):
    """基于DTW的相似度计算核心函数"""
    try:
        # 直接使用原始坐标数据（不插值对齐）
        x = data1[:, 1].reshape(-1, 1)  # 使用dB值作为特征
        y = data2[:, 1].reshape(-1, 1)

        # 执行DTW计算
        distance, path = fastdtw(x, y, radius=15, dist=euclidean)

        # 计算归一化相似度（0-1范围）
        max_len = max(len(x), len(y))
        norm_distance = distance / max_len  # 长度归一化
        # similarity = np.exp(-norm_distance)  # 指数映射到0-1
        similarity = 1 * (1 - distance / 1000)
        return similarity
    except Exception as e:
        print(f"DTW计算异常: {str(e)}")
        return 0.0

def compare_files(file_dict, threshold=0.7):
    """DTW比对主函数"""
    filenames = list(file_dict.keys())
    results = []
    
    for i in range(len(filenames)):
        for j in range(i+1, len(filenames)):
            sim = dtw_align_and_compare(file_dict[filenames[i]], 
                                      file_dict[filenames[j]])
            conclusion = "同缆" if sim > threshold else "不同缆"
            results.append(
                f"{filenames[i][:15]} vs {filenames[j][:15]} | "
                f"相似度: {sim:.2f} | 结论: {conclusion}"
            )
    return results

def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}

    # 遍历文件夹中的所有asc文件
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            data = process_otdr_file(file_path)
            if len(data) > 0:
                file_dict[filename] = data

    return file_dict




# 可视化函数调整（添加DTW路径显示）
def plot_dtw_path(data1, data2):
    """DTW路径可视化"""
    plt.figure(figsize=(12,6))
    
    # 原始曲线绘制
    plt.subplot(211)
    plt.plot(data1[:,0], data1[:,1], label='曲线A')
    plt.plot(data2[:,0], data2[:,1], label='曲线B')
    plt.title("原始OTDR曲线")
    plt.legend()
    
    # DTW路径绘制
    plt.subplot(212)
    x = data1[:,1].reshape(-1,1)
    y = data2[:,1].reshape(-1,1)
    distance, path = fastdtw(x, y, dist=euclidean)
    
    plt.imshow(np.zeros((len(x), len(y))), 
              cmap='gray_r', origin='lower')
    plt.plot([p[1] for p in path], [p[0] for p in path], 'r-')
    plt.title(f"DTW最优路径 (距离: {distance:.1f})")
    plt.xlabel("曲线B索引")
    plt.ylabel("曲线A索引")
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    folder_path = "C:/Users/<USER>/Desktop/co-cable/co-cable"
    file_data = process_folder(folder_path)
    
    # 示例：显示第一对曲线的DTW路径
    keys = list(file_data.keys())
    if len(keys) >= 2:
        plot_dtw_path(file_data[keys[0]], file_data[keys[3]])
        plot_all_curves(file_data)
    # 执行比对
    print("\nDTW同缆检测结果：")
    print("="*70)
    for res in compare_files(file_data):
        print(res)
    print("="*70)