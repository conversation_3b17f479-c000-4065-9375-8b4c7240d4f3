import os
import re
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from itertools import combinations
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc
import seaborn as sns
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class OTDRFeatureExtractor(nn.Module):
    def __init__(self, input_size=2, curve_output_size=4, event_output_size=3):
        super(OTDRFeatureExtractor, self).__init__()
        
        # 曲线特征提取部分
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=64,
            num_layers=2,
            batch_first=True,
            dropout=0.2
        )
        
        # 曲线特征压缩
        self.curve_fc = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, curve_output_size)
        )
        
        # 事件特征处理
        self.event_fc = nn.Sequential(
            nn.Linear(3, 16),  # 输入维度为3（类型、位置、损耗）
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(16, event_output_size)
        )
        
    def forward_curve(self, x):
        # 处理曲线数据
        _, h_n = self.gru(x)
        sequence_features = h_n[-1]
        curve_features = self.curve_fc(sequence_features)
        return curve_features
    
    def forward_event(self, events):
        # 处理事件数据
        event_features = self.event_fc(events)
        return event_features
    
    def forward(self, x, events=None):
        # 获取曲线特征
        curve_features = self.forward_curve(x)
        
        if events is not None:
            # 如果提供了事件数据，则同时处理事件特征
            event_features = self.forward_event(events)
            # 返回组合特征
            return curve_features, event_features
        
        return curve_features

# 初始化模型
model = OTDRFeatureExtractor().to(device)
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = nn.MSELoss()

# 设置已知的同缆组
CABLE_GROUPS = {
    1: ['ShundeYongfeng_DianxinNanqu_48F_01.asc', 'ShundeYongfeng_DianxinNanqu_48F_02.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_03.asc', 'ShundeYongfeng_DianxinNanqu_48F_04.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_05.asc', 'ShundeYongfeng_DianxinNanqu_48F_06.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_07.asc', 'ShundeYongfeng_DianxinNanqu_48F_08.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_09.asc', 'ShundeYongfeng_DianxinNanqu_48F_10.asc'],
    2: ['ShundeYongfeng_DianxinNanqu_48F_11.asc', 'ShundeYongfeng_DianxinNanqu_48F_12.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_23.asc', 'ShundeYongfeng_DianxinNanqu_48F_24.asc'],
    3: ['ShundeYongfeng_DianxinNanqu_48F_13.asc', 'ShundeYongfeng_DianxinNanqu_48F_14.asc'],
    4: ['ShundeYongfeng_DianxinNanqu_48F_18.asc', 'ShundeYongfeng_DianxinNanqu_48F_21.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_22.asc'],
    5: ['ShundeYongfeng_DianxinNanqu_48F_25.asc', 'ShundeYongfeng_DianxinNanqu_48F_26.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_27.asc', 'ShundeYongfeng_DianxinNanqu_48F_28.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_29.asc', 'ShundeYongfeng_DianxinNanqu_48F_30.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_31.asc', 'ShundeYongfeng_DianxinNanqu_48F_32.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_33.asc', 'ShundeYongfeng_DianxinNanqu_48F_34.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_35.asc', 'ShundeYongfeng_DianxinNanqu_48F_36.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_37.asc', 'ShundeYongfeng_DianxinNanqu_48F_38.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_39.asc', 'ShundeYongfeng_DianxinNanqu_48F_40.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_42.asc', 'ShundeYongfeng_DianxinNanqu_48F_45.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_47.asc', 'ShundeYongfeng_DianxinNanqu_48F_48.asc'],
    6: ['ShundeYongfeng_DianxinNanqu_48F_41.asc', 'ShundeYongfeng_DianxinNanqu_48F_43.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_44.asc'],
    7: ['ShundeYongfeng_DianxinNanqu_48F_46.asc'],
    8: ['ShundeYongfeng_JiangmenJianghai_48F_01.asc', 'ShundeYongfeng_JiangmenJianghai_48F_02.asc',
        'ShundeYongfeng_JiangmenJianghai_48F_06.asc', 'ShundeYongfeng_JiangmenJianghai_48F_08.asc',
        'ShundeYongfeng_JiangmenJianghai_48F_11.asc', 'ShundeYongfeng_JiangmenJianghai_48F_12.asc',
        'ShundeYongfeng_JiangmenJianghai_48F_27.asc', 'ShundeYongfeng_JiangmenJianghai_48F_38.asc',
        'ShundeYongfeng_JiangmenJianghai_48F_40.asc', 'ShundeYongfeng_JiangmenJianghai_48F_42.asc',
        'ShundeYongfeng_JiangmenJianghai_48F_43.asc', 'ShundeYongfeng_JiangmenJianghai_48F_45.asc',
        'ShundeYongfeng_JiangmenJianghai_48F_46.asc'],
    9: ['ShundeYongfeng_JiangmenJianghai_48F_03.asc', 'ShundeYongfeng_JiangmenJianghai_48F_07.asc'],
    10: ['ShundeYongfeng_JiangmenJianghai_48F_15.asc'],
    11: ['ShundeYongfeng_JiangmenJianghai_48F_17.asc'],
    12: ['ShundeYongfeng_JiangmenJianghai_48F_22.asc'],
    13: ['ShundeYongfeng_JiangmenJianghai_48F_31.asc'],
    14: ['ShundeYongfeng_GuangzhouQisuo_48F_01.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_04.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_06.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_09.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_11.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_14.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_19.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_20.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_21.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_23.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_24.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_25.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_26.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_28.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_29.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_30.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_31.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_32.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_35.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_36.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_37.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_38.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_40.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_41.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_42.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_43.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_44.asc'],
    15: ['ShundeYongfeng_GuangzhouQisuo_48F_02.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_39.asc'],
    16: ['ShundeYongfeng_GuangzhouQisuo_48F_13.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_33.asc'],
    17: ['ShundeYongfeng_GuangzhouQisuo_48F_45.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_46.asc'],
    18: ['ShundeYongfeng_GuangzhouQisuo_48F_03.asc'],
    19: ['ShundeYongfeng_GuangzhouQisuo_48F_18.asc']
}

def get_cable_group(filename):
    """获取文件所属的同缆组"""
    for group_id, files in CABLE_GROUPS.items():
        if filename in files:
            return group_id
    return -1  # 返回-1表示不属于任何已知组

def process_otdr_file(file_path):
    """处理单个OTDR文件,去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前10个点
    if len(data) > 10:
        data = data[10:]

    # 去除尾端50个点
    if len(data) > 50:
        data = data[:-50]

    return data

def extract_features(curve_data, feature_extractor):
    """从OTDR曲线中提取特征，同时考虑距离和dB值"""
    # 获取距离和dB值
    distances = curve_data[:, 0]  # 距离值
    values = curve_data[:, 1]     # dB值
    
    # 数据预处理
    # 标准化数据
    distances = (distances - np.mean(distances)) / np.std(distances)
    values = (values - np.mean(values)) / np.std(values)
    
    # 将数据转换为PyTorch张量并组合
    # 将距离和dB值组合成二维特征
    combined = np.stack([distances, values], axis=1)
    x = torch.FloatTensor(combined).to(device)
    x = x.view(1, -1, 2)  # shape: (batch_size=1, seq_length, input_size=2)
    
    # 使用模型提取特征
    with torch.no_grad():
        features = feature_extractor(x)
        
    # 将特征转换回NumPy数组
    return features.cpu().numpy().flatten()

def prepare_pair_features(file_dict, feature_extractor):
    """准备光纤对的特征数据和标签
    
    Returns:
        pairs_features: 光纤对的特征矩阵 (N, 14)，每行是[光纤1事件特征(3) + 光纤1曲线特征(4) + 光纤2事件特征(3) + 光纤2曲线特征(4)]
        pair_labels: 标签数组 (N,)，1表示同缆，0表示不同缆
        pair_names: 光纤对的文件名列表
    """
    pairs_features = []
    pair_labels = []
    pair_names = []
    
    # 首先为每个文件提取曲线特征
    curve_features_dict = {}
    events_dict = {}
    for filename, data in file_dict.items():
        # 提取曲线特征（4维）
        curve_features = extract_features(data, feature_extractor)
        curve_features_dict[filename] = curve_features
        
        # 提取事件特征（每个事件3维）
        events = extract_event_features(filename)
        events_dict[filename] = events
    
    # 生成所有可能的光纤对
    for (name1, data1), (name2, data2) in combinations(file_dict.items(), 2):
        events1 = events_dict[name1]
        events2 = events_dict[name2]
        curve1 = curve_features_dict[name1]
        curve2 = curve_features_dict[name2]
        
        # 如果两根光纤都有事件
        if len(events1) > 0 and len(events2) > 0:
            # 对光纤1的每个事件
            for event1 in events1:
                # 对光纤2的每个事件
                for event2 in events2:
                    # 组合特征：[光纤1事件特征(3) + 光纤1曲线特征(4) + 光纤2事件特征(3) + 光纤2曲线特征(4)]
                    combined_features = np.concatenate([
                        event1,          # 光纤1的事件特征（3维）
                        curve1,          # 光纤1的曲线特征（4维）
                        event2,          # 光纤2的事件特征（3维）
                        curve2           # 光纤2的曲线特征（4维）
                    ])
                    
                    # 判断是否属于同一根光缆
                    group1 = get_cable_group(name1)
                    group2 = get_cable_group(name2)
                    is_same_cable = int(group1 != -1 and group1 == group2)
                    
                    pairs_features.append(combined_features)
                    pair_labels.append(is_same_cable)
                    pair_names.append((name1, name2))
    
    return np.array(pairs_features), np.array(pair_labels), pair_names

def create_bootstrap_samples(X_curve, X_event_pairs, y, pair_names, n_samples=10):
    """使用替换采样创建多个数据子集
    
    Args:
        X_curve: 曲线特征矩阵
        X_event_pairs: 事件对特征矩阵列表
        y: 标签数组
        pair_names: 文件对名称
        n_samples: Bootstrap样本数量
        
    Returns:
        list of tuples: 每个元素包含(X_curve_bootstrap, X_event_pairs_bootstrap, y_bootstrap, names_bootstrap)
    """
    bootstrap_samples = []
    n_samples_total = len(X_curve)
    
    print(f"\n创建{n_samples}个Bootstrap样本（使用替换采样）...")
    
    for i in range(n_samples):
        # 生成随机索引（有放回采样）
        indices = np.random.choice(n_samples_total, size=n_samples_total, replace=True)
        
        # 使用这些索引创建bootstrap样本
        X_curve_bootstrap = X_curve[indices]
        X_event_pairs_bootstrap = [X_event_pairs[i] for i in indices]
        y_bootstrap = y[indices]
        names_bootstrap = [pair_names[i] for i in indices]
        
        # 统计正负样本比例
        positive_ratio = np.mean(y_bootstrap)
        print(f"Bootstrap样本 {i+1}: 总样本数={len(indices)}, 正样本比例={positive_ratio:.2%}")
        
        bootstrap_samples.append((X_curve_bootstrap, X_event_pairs_bootstrap, y_bootstrap, names_bootstrap))
        
    return bootstrap_samples

def train_and_evaluate_model(file_dict, feature_extractor):
    """使用新的分类方法训练和评估模型"""
    print("\n=== 开始模型训练和评估 ===")
    
    # 在文件级别进行训练测试集划分
    file_names = list(file_dict.keys())
    train_files, test_files = train_test_split(file_names, test_size=0.2, random_state=42)
        
    # 创建训练集和测试集的文件字典
    train_file_dict = {k: file_dict[k] for k in train_files}
    test_file_dict = {k: file_dict[k] for k in test_files}
    
    print(f"\n数据集划分:")
    print(f"训练集文件数: {len(train_files)}")
    print(f"测试集文件数: {len(test_files)}")
    
    # 准备训练集的特征数据
    pairs_data = []
    pair_labels = []
    pair_names = []
    
    total_train_files = len(train_files)
    total_pairs = total_train_files * (total_train_files - 1) // 2
    print(f"\n开始处理训练集文件对...")
    print(f"训练集文件数: {total_train_files}")
    print(f"需要处理的文件对数: {total_pairs}")
    
    processed_pairs = 0
    for (name1, data1), (name2, data2) in combinations(train_file_dict.items(), 2):
        processed_pairs += 1
        if processed_pairs % 10 == 0:
            print(f"进度: {processed_pairs}/{total_pairs} ({processed_pairs/total_pairs*100:.1f}%)")
        
        # 提取第一根光纤的特征
        curve_features1 = extract_features(data1, feature_extractor)  # 4维曲线特征
        events1 = extract_event_features(name1)  # 事件特征列表
        
        # 提取第二根光纤的特征
        curve_features2 = extract_features(data2, feature_extractor)  # 4维曲线特征
        events2 = extract_event_features(name2)  # 事件特征列表
        
        # 判断是否属于同一根光缆
        group1 = get_cable_group(name1)
        group2 = get_cable_group(name2)
        is_same_cable = int(group1 != -1 and group1 == group2)
        
        # 如果两根光纤都有事件特征
        if len(events1) > 0 and len(events2) > 0:
            for event1 in events1:
                for event2 in events2:
                    # 组合特征：[光纤1事件特征(3维) + 光纤1曲线特征(4维) + 光纤2事件特征(3维) + 光纤2曲线特征(4维)]
                    combined_features = np.concatenate([
                        event1,          # 光纤1的事件特征（3维）
                        curve_features1,  # 光纤1的曲线特征（4维）
                        event2,          # 光纤2的事件特征（3维）
                        curve_features2   # 光纤2的曲线特征（4维）
                    ])
                    pairs_data.append(combined_features)
                    pair_labels.append(is_same_cable)
                    pair_names.append((name1, name2))
    
    if not pairs_data:
        print("训练集中未找到有效的数据对！")
        return None, train_files, test_files
    
    # 转换为numpy数组
    X = np.array(pairs_data)
    y = np.array(pair_labels)
    
    # 创建并训练模型
    print("\n开始训练模型...")
    model = DLCRandomForest(n_trees=7)  # 使用n_trees棵树
    model.fit(X, y)
    
    # 在测试集上评估模型
    print("\n开始在测试集上评估模型...")
    
    # 准备测试集数据
    test_predictions = []
    test_true_labels = []
    test_pair_names = []
    
    # 对测试集中的每对文件进行预测
    for (name1, data1), (name2, data2) in combinations(test_file_dict.items(), 2):
        # 提取特征
        curve_features1 = extract_features(data1, feature_extractor)
        events1 = extract_event_features(name1)
        curve_features2 = extract_features(data2, feature_extractor)
        events2 = extract_event_features(name2)
        
        # 获取真实标签
        group1 = get_cable_group(name1)
        group2 = get_cable_group(name2)
        true_label = int(group1 != -1 and group1 == group2)
        
        # 如果两根光纤都有事件特征，进行预测
        if len(events1) > 0 and len(events2) > 0:
            pred = model.predict_with_voting(
                np.concatenate([events1[0], curve_features1, events2[0], curve_features2]),
                None,  # 这里不需要event_pairs参数
                name1,
                name2
            )
            test_predictions.append(pred)
            test_true_labels.append(true_label)
            test_pair_names.append((name1, name2))
    
    if test_predictions:
        # 计算测试集性能指标
        test_accuracy = np.mean(np.array(test_predictions) == np.array(test_true_labels))
        print(f"\n测试集评估结果:")
        print(f"测试样本数: {len(test_predictions)}")
        print(f"整体准确率: {test_accuracy:.4f}")
        
        # 计算并显示混淆矩阵
        cm = confusion_matrix(test_true_labels, test_predictions)
        print("\n混淆矩阵:")
        print(cm)
        
        # 计算每类的精确率和召回率
        print("\n分类报告:")
        print(classification_report(test_true_labels, test_predictions, target_names=['不同缆', '同缆']))
        
        # 分析错误预测
        print("\n错误预测分析:")
        errors = [(pair, true, pred) for pair, true, pred in zip(test_pair_names, test_true_labels, test_predictions) if true != pred]
        for (name1, name2), true_label, pred_label in errors:
            print(f"\n预测错误的文件对:")
            print(f"- {name1} (组别: {get_cable_group(name1)})")
            print(f"- {name2} (组别: {get_cable_group(name2)})")
            print(f"真实标签: {'同缆' if true_label == 1 else '不同缆'}")
            print(f"预测标签: {'同缆' if pred_label == 1 else '不同缆'}")
    else:
        print("\n测试集中没有可用于评估的样本对")
    
    # 只在训练集上进行分组预测
    predicted_groups = find_cable_groups(train_file_dict, model, feature_extractor)
    
    print("\n训练集分组结果：")
    print("=" * 65)
    for i, group in enumerate(predicted_groups, 1):
        print(f"\n第 {i} 组同缆文件:")
        for filename in group:
            true_group = get_cable_group(filename)
            print(f"  - {filename} (实际组别: {true_group if true_group != -1 else '未知'})")
        print("-" * 40)
    
    return model, train_files, test_files

# def plot_all_curves(file_dict):
#     """绘制所有曲线在同一图中"""
#     plt.figure(figsize=(12, 6))
#     colors = ['blue', 'green', 'red', 'purple', 'orange', 'brown']
    
#     # 按同缆组对文件进行分组
#     grouped_files = {}
#     for filename, data in file_dict.items():
#         group_id = get_cable_group(filename)
#         if group_id not in grouped_files:
#             grouped_files[group_id] = []
#         grouped_files[group_id].append((filename, data))
    
#     # 为每个组使用相同的颜色
#     for group_id, files in grouped_files.items():
#         color = colors[group_id % len(colors)] if group_id != -1 else 'gray'
#         for filename, data in files:
#             plt.plot(data[:, 0], data[:, 1],
#                     color=color,
#                     linewidth=1.5,
#                     alpha=0.7,
#                     label=f'{filename} (组{group_id if group_id != -1 else "未知"})')
    
#     plt.title("OTDR曲线综合比对", pad=20)
#     plt.xlabel("Position (km)")
#     plt.ylabel("Value (dB)")
#     plt.grid(True, alpha=0.3)
#     plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
#     plt.tight_layout()
#     plt.show()

def process_folder(folder_path):
    """处理整个文件夹及其子文件夹的OTDR文件
    
    Args:
        folder_path: 根文件夹路径
        
    Returns:
        dict: 以文件名为键，数据为值的字典
    """
    file_dict = {}
    processed_count = 0
    
    def process_single_file(file_path, prefix=""):
        """处理单个文件并返回数据"""
        try:
            data = process_otdr_file(file_path)
            if len(data) > 0:
                return data
        except Exception as e:
            print(f"处理文件失败: {os.path.basename(file_path)}")
        return None
    
    def walk_folder(current_path, parent_prefix=""):
        """递归遍历文件夹"""
        nonlocal processed_count
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)
            
            relative_path = os.path.relpath(os.path.dirname(item_path), folder_path)
            current_prefix = relative_path.replace("\\", "_").replace("/", "_")
            if current_prefix == ".":
                current_prefix = ""
            
            if os.path.isfile(item_path) and item.endswith(".asc"):
                data = process_single_file(item_path)
                if data is not None:
                    new_filename = f"{current_prefix}_{item}" if current_prefix else item
                    file_dict[new_filename] = data
                    processed_count += 1
            
            elif os.path.isdir(item_path):
                walk_folder(item_path)
    
    print(f"开始处理文件夹: {folder_path}")
    walk_folder(folder_path)
    print(f"成功处理 {processed_count} 个OTDR文件")
    
    return file_dict

def find_cable_groups(file_dict, model, feature_extractor):
    """根据模型预测结果找出同缆组"""
    all_files = list(file_dict.keys())
    groups = []
    used_files = set()
    
    # 首先提取所有文件的特征
    curve_features = {}
    events_features = {}
    for filename, data in file_dict.items():
        curve_features[filename] = extract_features(data, feature_extractor)
        events_features[filename] = extract_event_features(filename)
    
    for start_file in all_files:
        if start_file in used_files:
            continue
        
        current_group = {start_file}
        used_files.add(start_file)
        
        # 检查与其他所有文件的关系
        similar_files = []
        for other_file in all_files:
            if other_file == start_file or other_file in used_files:
                continue
            
            # 获取两根光纤的特征
            start_events = events_features[start_file]
            start_curve = curve_features[start_file]
            other_events = events_features[other_file]
            other_curve = curve_features[other_file]
            
            # 如果两根光纤都有事件特征
            if len(start_events) > 0 and len(other_events) > 0:
                # 预测是否同缆
                is_same_cable, confidence = model.predict_pair(
                    start_events, start_curve,
                    other_events, other_curve
                )
                
                if is_same_cable:
                    similar_files.append((other_file, confidence))
        
        # 添加到当前组（按置信度排序）
        similar_files.sort(key=lambda x: x[1], reverse=True)
        for other_file, _ in similar_files:
            if other_file not in used_files:
                current_group.add(other_file)
                used_files.add(other_file)
        
        if len(current_group) > 1:
            groups.append(sorted(list(current_group)))
    
    return groups

def predict_new_file(file_dict, model, new_filename, feature_extractor):
    """使用新的预测方法预测文件属于哪个同缆组"""
    if new_filename not in file_dict:
        print(f"错误：找不到文件 {new_filename}")
        return
    
    new_data = file_dict[new_filename]
    new_curve_features = extract_features(new_data, feature_extractor)
    new_events = extract_event_features(new_filename)
    
    print(f"\n预测文件 {new_filename} 的同缆关系：")
    print("=" * 65)
    
    results = []
    for other_filename, other_data in file_dict.items():
        if other_filename == new_filename:
            continue
            
        other_curve_features = extract_features(other_data, feature_extractor)
        other_events = extract_event_features(other_filename)
        
        if len(new_events) > 0 and len(other_events) > 0:
            predictions = []
            probabilities = []
            
            # 对每对事件进行预测
            for event1 in new_events:
                for event2 in other_events:
                    # 组合特征向量
                    combined_features = np.concatenate([
                        event1,
                        new_curve_features,
                        event2,
                        other_curve_features
                    ]).reshape(1, -1)
                    
                    # 获取预测概率
                    pred_proba = model.predict_proba(combined_features)[0]
                    pred = int(pred_proba[1] >= 0.5)
                    
                    predictions.append(pred)
                    probabilities.append(pred_proba[1])
            
            if predictions:
                # 统计预测结果
                positive_count = sum(predictions)
                total_count = len(predictions)
                
                # 如果正负样本数量相等，使用概率平均值
                if positive_count == total_count - positive_count:
                    avg_probability = np.mean(probabilities)
                    is_same_cable = avg_probability >= 0.5
                    confidence = abs(avg_probability - 0.5) * 2
                else:
                    # 使用多数投票
                    is_same_cable = positive_count > total_count / 2
                    confidence = abs(positive_count / total_count - 0.5) * 2
                
                if is_same_cable:
                    true_group = get_cable_group(other_filename)
                    results.append((other_filename, true_group, confidence))
    
    if results:
        print(f"发现 {len(results)} 个可能的同缆文件：")
        # 按置信度排序
        results.sort(key=lambda x: x[2], reverse=True)
        for filename, true_group, confidence in results:
            print(f"  - {filename} (实际组别: {true_group if true_group != -1 else '未知'}, 置信度: {confidence:.2%})")
        
        # 根据最高置信度的预测结果推荐组别
        most_confident = results[0]
        if most_confident[1] != -1:  # 如果最可信的文件有已知组别
            print(f"\n建议将 {new_filename} 归入第 {most_confident[1]} 组 (置信度: {most_confident[2]:.2%})")
    else:
        print("未找到同缆文件")

def analyze_prediction_errors(X_test, y_test, y_pred, pair_names_test, model):
    """分析预测错误的案例"""
    print("\n预测错误分析：")
    print("=" * 65)
    
    # 找出预测错误的索引
    error_indices = np.where(y_test != y_pred)[0]
    
    if len(error_indices) == 0:
        print("测试集中没有预测错误的案例！")
        return
    
    print(f"在测试集中发现 {len(error_indices)} 个预测错误：")
    
    for idx in error_indices:
        file1, file2 = pair_names_test[idx]
        true_label = y_test[idx]
        pred_label = y_pred[idx]
        
        print(f"\n错误案例 {idx + 1}:")
        print(f"文件对: {file1} 和 {file2}")
        print(f"真实情况: {'同缆' if true_label == 1 else '不同缆'}")
        print(f"预测结果: {'同缆' if pred_label == 1 else '不同缆'}")
        
        # 显示两个文件的实际组别
        group1 = get_cable_group(file1)
        group2 = get_cable_group(file2)
        print(f"实际组别: {file1} 属于组 {group1 if group1 != -1 else '未知'}, "
              f"{file2} 属于组 {group2 if group2 != -1 else '未知'}")
        print("-" * 40)

def evaluate_test_set(X_test, y_test, model, pair_names_test):
    """评估模型在测试集上的表现"""
    # 获取预测结果
    y_pred = model.predict(X_test)
    
    # 计算基本指标
    accuracy = np.mean(y_pred == y_test)
    
    print("\n测试集评估结果:")
    print("=" * 65)
    print(f"测试样本数量: {len(y_test)}")
    print(f"准确率 (Accuracy): {accuracy:.4f} ({accuracy:.2%})")
    
    # 创建并显示混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print("\n混淆矩阵:")
    print("-" * 40)
    print(f"真正例 (TP): {cm[1][1]}") # 预测为同缆，实际为同缆
    print(f"假正例 (FP): {cm[0][1]}") # 预测为同缆，实际为不同缆
    print(f"真负例 (TN): {cm[0][0]}") # 预测为不同缆，实际为不同缆
    print(f"假负例 (FN): {cm[1][0]}") # 预测为不同缆，实际为同缆
    
    # 计算F1分数
    precision = cm[1][1] / (cm[1][1] + cm[0][1]) if (cm[1][1] + cm[0][1]) > 0 else 0
    recall = cm[1][1] / (cm[1][1] + cm[1][0]) if (cm[1][1] + cm[1][0]) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    print("\n关键评估指标:")
    print("-" * 40)
    print(f"F1分数: {f1:.4f} - 模型整体性能的综合评价")
    
    # 绘制混淆矩阵热图
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=["不同缆", "同缆"], 
                yticklabels=["不同缆", "同缆"])
    plt.ylabel('真实标签')
    plt.xlabel('预测标签')
    plt.title('混淆矩阵')
    plt.show()
    
    return accuracy, f1

def train_feature_extractor(file_dict, feature_extractor, optimizer, criterion, epochs=150):
    """训练特征提取器"""
    print("开始训练特征提取器...")
    feature_extractor.train()
    
    # 准备训练数据
    all_curves = []
    curve_groups = {}  # 按同缆组整理数据
    
    for filename, data in file_dict.items():
        # 获取距离和dB值
        distances = data[:, 0]
        values = data[:, 1]
        
        # 标准化数据
        distances = (distances - np.mean(distances)) / np.std(distances)
        values = (values - np.mean(values)) / np.std(values)
        
        # 组合距离和dB值
        combined = np.stack([distances, values], axis=1)
        all_curves.append(combined)
        
        # 获取同缆组信息
        group_id = get_cable_group(filename)
        if group_id not in curve_groups:
            curve_groups[group_id] = []
        curve_groups[group_id].append(len(all_curves) - 1)  # 存储索引
    
    # 将数据转换为PyTorch张量
    curves_tensor = []
    for curve in all_curves:
        x = torch.FloatTensor(curve).view(1, -1, 2)
        curves_tensor.append(x)
    
    def get_triplet_samples():
        """获取三元组样本：锚点、正样本（同组）、负样本（不同组）"""
        # 随机选择一个有多个曲线的组
        valid_groups = [g for g in curve_groups.keys() if len(curve_groups[g]) > 1]
        if not valid_groups:
            return None, None, None
            
        anchor_group = np.random.choice(valid_groups)
        # 选择锚点和正样本
        anchor_idx, pos_idx = np.random.choice(curve_groups[anchor_group], 2, replace=False)
        
        # 选择负样本（来自不同组）
        neg_group = np.random.choice([g for g in valid_groups if g != anchor_group])
        neg_idx = np.random.choice(curve_groups[neg_group])
        
        return anchor_idx, pos_idx, neg_idx
    
    # 定义三元组损失
    def triplet_loss(anchor, positive, negative, margin=1.0):
        pos_dist = torch.nn.functional.mse_loss(anchor, positive)
        neg_dist = torch.nn.functional.mse_loss(anchor, negative)
        loss = torch.clamp(pos_dist - neg_dist + margin, min=0.0)
        return loss
    
    # 训练循环
    for epoch in range(epochs):
        total_loss = 0
        n_batches = 0
        
        # 每个epoch进行多次三元组训练
        for _ in range(len(all_curves)):
            # 获取三元组样本
            anchor_idx, pos_idx, neg_idx = get_triplet_samples()
            if anchor_idx is None:
                continue
                
            # 获取完整曲线
            anchor = curves_tensor[anchor_idx].to(device)
            positive = curves_tensor[pos_idx].to(device)
            negative = curves_tensor[neg_idx].to(device)
            
            # 前向传播
            optimizer.zero_grad()
            anchor_feat = feature_extractor(anchor)
            pos_feat = feature_extractor(positive)
            neg_feat = feature_extractor(negative)
            
            # 计算三元组损失
            loss = triplet_loss(anchor_feat, pos_feat, neg_feat)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            n_batches += 1
        
        if n_batches > 0 and (epoch + 1) % 10 == 0:
            print(f"Epoch [{epoch+1}/{epochs}], Loss: {total_loss/n_batches:.4f}")
    
    print("特征提取器训练完成！")
    feature_extractor.eval()

def extract_event_features(file_path):
    """提取OTDR曲线的事件特征
    
    Args:
        file_path: OTDR文件路径或文件名
        
    Returns:
        events_features: 包含事件特征的列表，每个事件包含[类型编码, 位置, 损耗]
    """
    events_features = []
    event_type_map = {
        'Non-Reflective Fault': 1,
        'Reflective Fault': 2,
        'Positive Fault': 3,
    }
    
    try:
        # 处理文件路径
        if os.path.isfile(file_path):
            actual_path = file_path
        else:
            # 如果是文件名，尝试在不同的可能路径中查找
            base_name = os.path.basename(file_path)  # 获取文件名部分
            if '_' in base_name:
                # 如果文件名包含下划线，尝试提取最后一部分
                parts = base_name.split('_')
                simple_name = parts[-1]  # 获取最后一部分（如"45.asc"）
                folder_name = '_'.join(parts[:-1])  # 获取文件夹名称
                
                # 构建可能的路径
                possible_paths = [
                    os.path.join("C:/Users/<USER>/Desktop/干线测试", folder_name, simple_name),
                    os.path.join("C:/Users/<USER>/Desktop/干线测试", base_name),
                    os.path.join("C:/Users/<USER>/Desktop/干线测试", simple_name)
                ]
                
                # 尝试所有可能的路径
                for path in possible_paths:
                    if os.path.isfile(path):
                        actual_path = path
                        break
                else:
                    print(f"找不到文件: {file_path}")
                    return []
            else:
                print(f"无效的文件名格式: {file_path}")
                return []
        
        with open(actual_path, 'r') as f:
            content = f.read()
            lines = content.split('\n')
            
            # 找到FAULT TABLE的起始位置
            start_idx = None
            for i, line in enumerate(lines):
                if 'FAULT TABLE' in line:
                    start_idx = i
                    break
            
            if start_idx is None:
                return []
            
            # 跳过表头，直到找到事件数据
            current_idx = start_idx
            while current_idx < len(lines):
                if any(keyword in lines[current_idx].upper() for keyword in ['NO.', 'NO,']):
                    break
                current_idx += 1
            
            current_idx += 1  # 跳过表头行
            
            # 解析事件
            while current_idx < len(lines):
                line = lines[current_idx].strip()
                if not line:  # 跳过空行
                    current_idx += 1
                    continue
                
                # 如果遇到Link Loss行或者Summary行，说明事件表结束
                if any(keyword in line.upper() for keyword in ['LINK LOSS', 'SUMMARY', 'END']):
                    break
                
                # 尝试不同的分隔方式解析
                parts = []
                if ',' in line:  # 使用逗号分隔
                    parts = [part.strip() for part in line.split(',')]
                else:  # 使用空格分隔
                    parts = [part.strip() for part in line.split() if part.strip()]
                
                if len(parts) >= 4:  # 至少需要4个字段（编号、位置、类型、损耗）
                    try:
                        # 检查第一个字段是否为数字（事件编号）
                        if parts[0].replace('.', '').isdigit():
                            # 提取位置（通常是第二个字段）
                            location = float(parts[1].replace('km', '').strip())
                            
                            # 查找事件类型（通常是第三个字段）
                            event_type = None
                            event_code = None
                            for type_str, code in event_type_map.items():
                                if type_str.lower() in parts[2].lower():
                                    event_type = type_str
                                    event_code = code
                                    break
                            
                            # 提取损耗值（通常是第四个字段）
                            loss = 0.0
                            if len(parts) >= 4:
                                loss_str = parts[3].strip()
                                # 移除可能的单位和特殊字符
                                loss_str = loss_str.lower().replace('db', '').replace('- - -', '0').strip()
                                try:
                                    loss = float(loss_str) if loss_str else 0.0
                                except ValueError:
                                    pass
                            
                            if event_code:
                                events_features.append([event_code, location, loss])
                                
                    except (ValueError, IndexError):
                        pass
                
                current_idx += 1
                
    except Exception as e:
        print(f"处理文件时出现错误: {str(e)}")
    
    return events_features

def print_events_features(file_path):
    """打印事件特征，用于测试"""
    print(f"\n处理文件: {file_path}")
    events = extract_event_features(file_path)
    print(f"成功提取 {len(events)} 个事件")

def prepare_event_pair_features(events1, events2, feature_extractor):
    """准备事件对的特征矩阵
    
    Args:
        events1: 第一条光纤的事件列表
        events2: 第二条光纤的事件列表
        feature_extractor: 特征提取器
        
    Returns:
        event_pairs_features: 事件对特征矩阵
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    event_pairs_features = []
    
    # 将事件数据转换为张量
    for event1 in events1:
        event1_tensor = torch.FloatTensor(event1).to(device)
        for event2 in events2:
            event2_tensor = torch.FloatTensor(event2).to(device)
            
            # 提取每对事件的特征
            with torch.no_grad():
                event1_features = feature_extractor.forward_event(event1_tensor.unsqueeze(0))
                event2_features = feature_extractor.forward_event(event2_tensor.unsqueeze(0))
                
            # 组合特征
            pair_features = torch.cat([event1_features, event2_features], dim=1)
            event_pairs_features.append(pair_features.cpu().numpy().flatten())
    
    return np.array(event_pairs_features)

class DLCRandomForest:
    def __init__(self, n_trees=3):
        self.n_trees = n_trees
        self.trees = []
        self.scalers = []
        
    def fit(self, X, y):
        """训练DLC-RF模型
        
        Args:
            X: 特征矩阵
            y: 标签数组
        """
        print(f"\n开始训练DLC-RF模型（{self.n_trees}棵树）...")
        
        # 计算特征维度
        total_features = X.shape[1]
        curve_features_dim = 4  # 曲线特征是4维
        event_pair_dim = 6     # 事件对特征是6维（每个事件3维）
        max_features = int(np.sqrt(total_features))  # 特征随机选择数量
        
        print(f"\n模型配置:")
        print(f"- 树的深度: 6层")
        print(f"- 分裂标准: 基尼杂质")
        print(f"- 曲线特征维度: {curve_features_dim}")
        print(f"- 事件对特征维度: {event_pair_dim}")
        print(f"- 特征总数: {total_features}")
        print(f"- 节点分裂时随机选择的特征数: {max_features}")
        
        # 对每个bootstrap样本训练一个决策树
        for i in range(self.n_trees):
            print(f"\n训练第 {i+1}/{self.n_trees} 棵树")
            
            # 生成bootstrap样本
            indices = np.random.choice(len(X), size=len(X), replace=True)
            X_bootstrap = X[indices]
            y_bootstrap = y[indices]
            
            print(f"Bootstrap样本大小: {len(X_bootstrap)}")
            
            # 为每个树创建独立的数据标准化器
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_bootstrap)
            
            # 创建并训练决策树
            tree = RandomForestClassifier(
                n_estimators=1,
                max_depth=6,
                criterion='gini',
                max_features=max_features,
                random_state=42+i,
                class_weight='balanced'
            )
            
            # 训练决策树
            tree.fit(X_scaled, y_bootstrap)
            
            # 存储训练好的树和对应的标准化器
            self.trees.append(tree)
            self.scalers.append(scaler)
            
            # 评估当前树的性能
            y_pred = tree.predict(X_scaled)
            accuracy = np.mean(y_pred == y_bootstrap)
            print(f"树 {i+1} 训练准确率: {accuracy:.4f}")
        
        print("\nDLC-RF模型训练完成！")

    def predict_with_voting(self, curve_features, event_pairs, file1, file2):
        """使用投票机制预测两条曲线是否属于同一根光缆"""
        votes = []
        for i, (tree, scaler) in enumerate(zip(self.trees, self.scalers)):
            tree_predictions = []
            X_scaled = scaler.transform(curve_features.reshape(1, -1))
            pred = tree.predict(X_scaled)[0]
            tree_predictions.append(pred)
            tree_vote = np.mean(tree_predictions) >= 0.5
            votes.append(tree_vote)
        
        # 最终投票结果
        final_prediction = np.mean(votes) >= 0.5
        return final_prediction

    def predict_pair(self, fiber1_events, fiber1_curve, fiber2_events, fiber2_curve):
        """预测两根光纤是否属于同一根光缆
        
        Args:
            fiber1_events: 光纤1的事件特征列表，每个事件3维
            fiber1_curve: 光纤1的曲线特征，4维
            fiber2_events: 光纤2的事件特征列表，每个事件3维
            fiber2_curve: 光纤2的曲线特征，4维
        
        Returns:
            is_same_cable: 是否同缆
            confidence: 置信度
        """
        predictions = []
        probabilities = []
        
        # 对每个事件对进行预测
        for event1 in fiber1_events:
            for event2 in fiber2_events:
                # 组合特征向量
                combined_features = np.concatenate([
                    event1,          # 光纤1的事件特征（3维）
                    fiber1_curve,    # 光纤1的曲线特征（4维）
                    event2,          # 光纤2的事件特征（3维）
                    fiber2_curve     # 光纤2的曲线特征（4维）
                ]).reshape(1, -1)
                
                # 使用每棵树进行预测
                tree_predictions = []
                tree_probabilities = []
                for tree, scaler in zip(self.trees, self.scalers):
                    # 标准化特征
                    X_scaled = scaler.transform(combined_features)
                    
                    # 获取预测概率
                    pred_proba = tree.predict_proba(X_scaled)[0]
                    pred = int(pred_proba[1] >= 0.5)
                    
                    tree_predictions.append(pred)
                    tree_probabilities.append(pred_proba[1])
                
                # 使用所有树的平均预测
                avg_probability = np.mean(tree_probabilities)
                pred = int(avg_probability >= 0.5)
                
                predictions.append(pred)
                probabilities.append(avg_probability)
        
        # 统计预测结果
        positive_count = sum(predictions)
        total_count = len(predictions)
        
        # 如果正负样本数量相等，使用概率平均值
        if positive_count == total_count - positive_count:
            avg_probability = np.mean(probabilities)
            is_same_cable = avg_probability >= 0.5
            confidence = abs(avg_probability - 0.5) * 2
        else:
            # 使用多数投票
            is_same_cable = positive_count > total_count / 2
            confidence = abs(positive_count / total_count - 0.5) * 2
        
        return is_same_cable, confidence

    def predict_proba(self, X):
        """预测样本属于每个类别的概率
        
        Args:
            X: 特征矩阵
            
        Returns:
            probabilities: 每个样本属于每个类别的概率
        """
        # 存储每棵树的概率预测
        tree_probas = []
        
        # 使用每棵树进行预测
        for tree, scaler in zip(self.trees, self.scalers):
            # 标准化特征
            X_scaled = scaler.transform(X)
            # 获取预测概率
            probas = tree.predict_proba(X_scaled)
            tree_probas.append(probas)
        
        # 计算所有树的平均概率
        avg_probas = np.mean(tree_probas, axis=0)
        return avg_probas
    
    def predict(self, X):
        """预测样本的类别
        
        Args:
            X: 特征矩阵
            
        Returns:
            predictions: 预测的类别标签
        """
        # 获取预测概率
        probas = self.predict_proba(X)
        # 使用概率阈值0.5进行分类
        predictions = (probas[:, 1] >= 0.5).astype(int)
        return predictions

if __name__ == "__main__":
    print("\n=== 开始主程序 ===")
    # 清空之前的比较结果文件
    with open('comparison_results.txt', 'w', encoding='utf-8') as f:
        f.write("OTDR曲线对比结果\n")
        f.write("=" * 50 + "\n")
    
    folder_path = "C:/Users/<USER>/Desktop/干线测试" 
    file_data = process_folder(folder_path)
    
    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        print(f"成功读取 {len(file_data)} 个OTDR文件")
        
        # # 显示所有曲线（按组着色）
        # plot_all_curves(file_data)
        
        try:
            # 初始化特征提取器
            feature_extractor = OTDRFeatureExtractor().to(device)
            optimizer = torch.optim.Adam(feature_extractor.parameters(), lr=0.001)
            criterion = nn.MSELoss()
            
            # 训练特征提取器
            train_feature_extractor(file_data, feature_extractor, optimizer, criterion)
            
            # 使用新的分类方法训练和评估模型
            model, train_files, test_files = train_and_evaluate_model(file_data, feature_extractor)
            
            if model is not None:
                # 只检测训练集中的未知组别文件
                unknown_files = [f for f in train_files if get_cable_group(f) == -1]
                if unknown_files:
                    print("\n发现训练集中的未知组别文件，开始预测：")
                    print("=" * 65)
                    for unknown_file in unknown_files:
                        predict_new_file({k: file_data[k] for k in train_files}, model, unknown_file, feature_extractor)
            
        except Exception as e:
            print(f"程序执行过程中出现错误: {str(e)}") 