#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SMOTE工作原理详细说明
展示SMOTE如何为同缆识别生成合成数据
"""

import numpy as np
import matplotlib.pyplot as plt
from collections import Counter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

def explain_smote_for_cable_detection():
    """详细解释SMOTE在同缆识别中的应用"""
    
    print("=" * 60)
    print("SMOTE在同缆识别中生成的数据类型详解")
    print("=" * 60)
    
    print("\n1. 原始数据结构:")
    print("   每个光纤对的特征向量 (16维):")
    print("   ┌─────────────────────────────────────────────────────┐")
    print("   │ 光纤1事件特征(4维) │ 光纤1曲线特征(4维) │")
    print("   │ [类型,位置,损耗,反射] │ [深度学习提取特征] │")
    print("   ├─────────────────────────────────────────────────────┤")
    print("   │ 光纤2事件特征(4维) │ 光纤2曲线特征(4维) │")
    print("   │ [类型,位置,损耗,反射] │ [深度学习提取特征] │")
    print("   └─────────────────────────────────────────────────────┘")
    
    print("\n2. 数据不平衡问题:")
    print("   - 不同缆对: 数量很多 (例如: 10000个)")
    print("   - 同缆对:   数量很少 (例如: 500个)")
    print("   - 不平衡比例: 20:1")
    
    print("\n3. SMOTE生成的合成数据:")
    print("   SMOTE会生成新的'同缆对'特征向量，这些向量:")
    print("   ✓ 不是真实存在的光纤对")
    print("   ✓ 是基于现有同缆对特征插值生成的")
    print("   ✓ 保持同缆对的特征分布特性")
    print("   ✓ 帮助模型更好地学习同缆对的模式")

def simulate_smote_process():
    """模拟SMOTE的生成过程"""
    
    print("\n" + "=" * 60)
    print("SMOTE生成过程模拟")
    print("=" * 60)
    
    # 模拟原始同缆对数据 (简化为2维便于可视化)
    np.random.seed(42)
    
    # 原始同缆对特征 (少数类)
    original_same_cable = np.array([
        [2.1, 3.2],  # 同缆对1的特征
        [2.3, 3.1],  # 同缆对2的特征  
        [1.9, 3.4],  # 同缆对3的特征
        [2.2, 2.9],  # 同缆对4的特征
        [2.0, 3.3],  # 同缆对5的特征
    ])
    
    print(f"\n原始同缆对数据 ({len(original_same_cable)}个):")
    for i, sample in enumerate(original_same_cable):
        print(f"   同缆对{i+1}: [{sample[0]:.1f}, {sample[1]:.1f}]")
    
    # 手动模拟SMOTE生成过程
    print(f"\nSMOTE生成过程:")
    print("1. 对每个同缆对样本，找到k个最近邻")
    print("2. 随机选择一个邻居")
    print("3. 在样本和邻居之间随机插值生成新样本")
    
    synthetic_samples = []
    for i, sample in enumerate(original_same_cable):
        # 找最近邻 (简化：随机选择另一个样本)
        neighbor_idx = (i + 1) % len(original_same_cable)
        neighbor = original_same_cable[neighbor_idx]
        
        # 随机插值 (λ ∈ [0,1])
        lambda_val = np.random.random()
        synthetic = sample + lambda_val * (neighbor - sample)
        synthetic_samples.append(synthetic)
        
        print(f"   生成样本{i+1}: 在样本{i+1}和样本{neighbor_idx+1}之间插值")
        print(f"      λ={lambda_val:.2f}, 新样本=[{synthetic[0]:.1f}, {synthetic[1]:.1f}]")
    
    synthetic_samples = np.array(synthetic_samples)
    
    # 可视化
    plt.figure(figsize=(12, 5))
    
    # 原始数据
    plt.subplot(1, 2, 1)
    plt.scatter(original_same_cable[:, 0], original_same_cable[:, 1], 
                c='blue', s=100, alpha=0.7, label='原始同缆对')
    plt.title('原始同缆对数据')
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # SMOTE后数据
    plt.subplot(1, 2, 2)
    plt.scatter(original_same_cable[:, 0], original_same_cable[:, 1], 
                c='blue', s=100, alpha=0.7, label='原始同缆对')
    plt.scatter(synthetic_samples[:, 0], synthetic_samples[:, 1], 
                c='red', s=100, alpha=0.7, marker='^', label='SMOTE生成')
    
    # 绘制连接线显示插值关系
    for i, sample in enumerate(original_same_cable):
        neighbor_idx = (i + 1) % len(original_same_cable)
        neighbor = original_same_cable[neighbor_idx]
        plt.plot([sample[0], neighbor[0]], [sample[1], neighbor[1]], 
                'gray', alpha=0.3, linestyle='--')
    
    plt.title('SMOTE生成后的数据')
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return original_same_cable, synthetic_samples

def explain_benefits():
    """解释SMOTE的好处"""
    
    print("\n" + "=" * 60)
    print("SMOTE在同缆识别中的好处")
    print("=" * 60)
    
    print("\n1. 解决类别不平衡问题:")
    print("   ✓ 增加同缆对样本数量")
    print("   ✓ 平衡训练数据分布")
    print("   ✓ 防止模型偏向多数类(不同缆对)")
    
    print("\n2. 提高模型性能:")
    print("   ✓ 提高同缆对的识别率(召回率)")
    print("   ✓ 减少将同缆对误判为不同缆对的情况")
    print("   ✓ 整体提升分类准确性")
    
    print("\n3. 保持数据质量:")
    print("   ✓ 生成的样本符合同缆对的特征分布")
    print("   ✓ 不会引入噪声或异常值")
    print("   ✓ 保持特征之间的相关性")
    
    print("\n4. 在您的项目中:")
    print("   ✓ 原始数据: 同缆对很少，不同缆对很多")
    print("   ✓ SMOTE后: 同缆对和不同缆对数量平衡")
    print("   ✓ 模型能更好地学习同缆对的特征模式")
    print("   ✓ 提高对新光纤对的同缆识别准确性")

def show_real_world_example():
    """展示实际应用中的例子"""
    
    print("\n" + "=" * 60)
    print("实际应用示例")
    print("=" * 60)
    
    print("\n假设您的数据集中:")
    print("- 总光纤文件: 100个")
    print("- 可能的光纤对: 100×99/2 = 4950对")
    print("- 同缆对: 约200对 (4%)")
    print("- 不同缆对: 约4750对 (96%)")
    
    print("\nSMOTE处理后:")
    print("- 原始同缆对: 200个")
    print("- SMOTE生成的同缆对: 4550个")
    print("- 总同缆对: 4750个")
    print("- 不同缆对: 4750个")
    print("- 平衡比例: 1:1")
    
    print("\n生成的每个合成同缆对包含:")
    print("1. 光纤A的事件特征: [事件类型, 位置(km), 损耗(dB), 反射率(dB)]")
    print("2. 光纤A的曲线特征: [深度学习提取的4维特征]")
    print("3. 光纤B的事件特征: [事件类型, 位置(km), 损耗(dB), 反射率(dB)]")
    print("4. 光纤B的曲线特征: [深度学习提取的4维特征]")
    
    print("\n这些合成数据:")
    print("✓ 不对应真实存在的光纤对")
    print("✓ 但具有真实同缆对的统计特性")
    print("✓ 帮助模型学习同缆对的判别模式")

def main():
    """主函数"""
    explain_smote_for_cable_detection()
    original, synthetic = simulate_smote_process()
    explain_benefits()
    show_real_world_example()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print("SMOTE生成的是合成的同缆光纤对特征数据，")
    print("这些数据帮助平衡训练集，提高模型对同缆对的识别能力。")
    print("=" * 60)

if __name__ == "__main__":
    main()
