import os
import re
import numpy as np
from scipy.spatial.distance import euclidean
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from itertools import combinations

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


def plot_all_curves(file_dict, ref_file=None):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())

    # 生成不同颜色
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    # 绘制所有曲线
    for idx, filename in enumerate(filenames):
        data = file_dict[filename]
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    # 设置图表属性
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能调整图例位置
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    plt.show()


def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前5个点
    if len(data) > 5:
        data = data[5:]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    return data


def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}

    # 遍历文件夹中的所有asc文件
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            data = process_otdr_file(file_path)
            if len(data) > 0:
                file_dict[filename] = data

    return file_dict


def compare_files(file_dict, threshold=1000.0):
    """
    DTW相似度比对（包含二维坐标比较）
    参数说明：
    file_dict: 包含所有文件数据的字典
    threshold: 判定为同缆的差异度阈值（可根据实测数据调整）
    """
    filenames = list(file_dict.keys())
    results = []

    # 遍历所有文件组合
    for (file1, file2) in combinations(filenames, 2):
        data1 = file_dict[file1]
        data2 = file_dict[file2]

        # 计算DTW距离（同时考虑位置和值的二维差异）
        distance, _ = fastdtw(data1, data2, dist=euclidean)

        # 存储结果
        results.append((file1, file2, distance))

    # 按差异度升序排序
    results.sort(key=lambda x: x[2])

    # 格式化输出结果
    formatted_results = []
    for file1, file2, dist in results:
        conclusion = "同缆" if dist < threshold else "不同缆"
        formatted_results.append(f"{file1} vs {file2} | 差异度: {dist:.2f} | 结论: {conclusion}")

    return formatted_results


if __name__ == "__main__":
    folder_path = "C:/Users/<USER>/Desktop/co-cable/co-cable"
    file_data = process_folder(folder_path)

    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        plot_all_curves(file_data)
        print("\n同缆检测结果（按相似度排序）：")
        print("=" * 65)
        # 此处可调整threshold参数（默认1000）
        for result in compare_files(file_data, threshold=1000):
            print(result)
        print("=" * 65)
        print(f"共完成 {len(file_data)} 个文件的 {len(file_data) * (len(file_data) - 1) // 2} 次比对")