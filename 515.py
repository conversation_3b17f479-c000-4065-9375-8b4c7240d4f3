import os
import re
import numpy as np
from scipy.spatial.distance import euclidean
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from itertools import combinations

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# ---------------------------- 类型相似度配置 ----------------------------
TYPE_SIMILARITY = {
    # 精确匹配
    ('Non-Reflective Fault', 'Non-Reflective Fault'): 1.0,
    ('Reflective Fault', 'Reflective Fault'): 1.0,
    ('Positive Fault', 'Positive Fault'): 1.0,

    # 反射相关类型差异
    ('Non-Reflective Fault', 'Reflective Fault'): 0,

    # 跨类型差异
    ('Non-Reflective Fault', 'Positive Fault'): 0,
    ('Reflective Fault', 'Positive Fault'): 0,

    # 默认相似度（用于未知类型）
    'default': 0
}


# ---------------------------- 事件解析增强 ----------------------------
def parse_fault_table(content):
    """支持三种标准故障类型的解析"""
    events = []
    pattern = re.compile(
        r"^\s*(\d+),\s*([\d.]+),\s*(Non-?Reflective Fault|Reflective Fault|Positive Fault|.*?Fault|Fiber End|Macro Bend|.*Loss),\s*(-?[\d.]+)",
        re.IGNORECASE
    )

    # 类型标准化映射
    type_mapping = {
        'Nonreflective Fault': 'Non-Reflective Fault',
        'Pos Fault': 'Positive Fault',
        'Reflection Fault': 'Reflective Fault',
        'Fiber End': 'Non-Reflective Fault'  # 保持向下兼容
    }

    for line in content.split('\n'):
        if re.search(r",\s*-{2,}\s*,", line):
            continue

        match = pattern.search(line)
        if match:
            raw_type = match.group(3).strip().title()

            # 处理混合类型（如"Reflective/Non-Reflective"）
            if '/' in raw_type:
                main_type = raw_type.split('/')[0].strip()
                raw_type = main_type

            # 类型标准化
            event_type = type_mapping.get(raw_type, raw_type)

            event = {
                'position': float(match.group(2)),
                'type': event_type,
                'loss': float(match.group(4)),
                'refl': None
            }
            events.append(event)
    return events

# ---------------------------- 修改文件处理流程 ----------------------------
def process_otdr_file(file_path):
    """处理单个OTDR文件，返回数据和事件表"""
    # 编码检测
    encodings = ['gb18030', 'utf-8', 'latin-1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                # 验证关键字段
                if "POS(km)" in content and "FAULT TABLE" in content:
                    break
        except (UnicodeDecodeError, LookupError) as e:
            last_error = e
            continue
    else:
        raise ValueError(f"解码失败: {file_path} \n{last_error}")
    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前5个点
    if len(data) > 5:
        data = data[5:]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    # 解析事件表
    events = parse_fault_table(content)

    return {'data': data, 'events': events}


def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}

    # 遍历文件夹中的所有asc文件
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            result = process_otdr_file(file_path)
            if len(result['data']) > 0:
                file_dict[filename] = result

    return file_dict


# ---------------------------- 新增事件对比函数 ----------------------------
def event_similarity(events1, events2):
    """基于类型矩阵的相似度计算"""
    # 特征过滤：保留有效故障事件
    valid_events = lambda e: (e['loss'] is not None) and (0.001 < abs(e['loss']) < 5.0)
    list1 = sorted([e for e in events1 if valid_events(e)], key=lambda x: x['position'])
    list2 = sorted([e for e in events2 if valid_events(e)], key=lambda x: x['position'])

    # 动态窗口匹配
    matched_pairs = []
    i = j = 0
    while i < len(list1) and j < len(list2):
        pos_diff = list1[i]['position'] - list2[j]['position']
        if abs(pos_diff) <= 0.15:
            matched_pairs.append((list1[i], list2[j]))
            i += 1
            j += 1
        elif pos_diff < 0:
            i += 1
        else:
            j += 1

    # 新型相似度计算
    total_score = 0
    for e1, e2 in matched_pairs:
        # 位置相似度（标准差缩小到0.08）
        pos_sim = np.exp(-(e1['position'] - e2['position'])**2 / (2 * (0.08**2)))

        # 损耗相似度（强化小差异敏感性）
        loss_diff = abs(e1['loss'] - e2['loss'])
        loss_sim = 1 / (1 + 8 * loss_diff)  # 系数从10调整为8

        # 类型相似度（基于配置矩阵）
        type_pair = tuple(sorted([e1['type'], e2['type']]))  # 排序消除方向影响
        type_sim = TYPE_SIMILARITY.get(type_pair, TYPE_SIMILARITY['default'])

        # 权重分配调整（类型权重提升到15%）
        total_score += 0.4 * pos_sim + 0.45 * loss_sim + 0.15 * type_sim

    # 惩罚系数优化
    count_penalty = 1 - abs(len(list1) - len(list2)) / (len(list1) + len(list2) + 1e-5)  # 避免除零
    return (total_score / len(matched_pairs)) * count_penalty if matched_pairs else 0
# ---------------------------- 修改比对主逻辑 ----------------------------
def compare_files(file_dict, dtw_threshold=1000.0, sim_threshold=0.7):
    """
    改进版比对逻辑（同时考虑曲线和事件）
    参数说明：
    - dtw_threshold: 曲线差异阈值（默认1000）
    - sim_threshold: 事件相似度阈值（默认0.7）
    """
    filenames = list(file_dict.keys())
    results = []

    for (file1, file2) in combinations(filenames, 2):
        data1 = file_dict[file1]['data']
        data2 = file_dict[file2]['data']
        events1 = file_dict[file1]['events']
        events2 = file_dict[file2]['events']

        # 计算曲线差异
        dtw_dist, _ = fastdtw(data1, data2, dist=euclidean)

        # 计算事件相似度
        event_sim = event_similarity(events1, events2)

        # 综合判断逻辑
        curve_pass = dtw_dist < dtw_threshold
        event_pass = event_sim > sim_threshold

        conclusion = "同缆" if (curve_pass and event_pass) else "不同缆"

        results.append((
            file1, file2,
            dtw_dist,
            event_sim,
            conclusion
        ))

    # 按综合条件排序（优先曲线差异小，其次事件相似度高）
    results.sort(key=lambda x: (x[2], -x[3]))

    # 格式化输出
    formatted_results = []
    for res in results:
        formatted_results.append(
            f"{res[0]} vs {res[1]} | "
            f"曲线差异: {res[2]:.1f} | "
            f"事件相似度: {res[3]:.2f} | "
            f"结论: {res[4]}"
        )

    return formatted_results


# ---------------------------- 增强可视化功能 ----------------------------
def plot_all_curves(file_dict):
    """绘制所有曲线及事件标记"""
    plt.figure(figsize=(14, 7))
    colors = plt.cm.tab10.colors
    markers = ['o', 's', '^', 'v', 'D', 'p']

    for idx, filename in enumerate(file_dict):
        data = file_dict[filename]['data']
        events = file_dict[filename]['events']

        # 绘制主曲线
        plt.plot(data[:, 0], data[:, 1],
                 color=colors[idx % 10],
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

        # 标注事件点
        event_x = [e['position'] for e in events]
        event_y = np.interp(event_x, data[:, 0], data[:, 1])

        plt.scatter(event_x, event_y,
                    color=colors[idx % 10],
                    marker=markers[idx % 6],
                    s=80,
                    edgecolor='white',
                    zorder=3,
                    alpha=0.9)

        # 添加事件标注
        for e in events:
            plt.annotate(e['type'],
                         (e['position'], np.interp(e['position'], data[:, 0], data[:, 1])),
                         textcoords="offset points",
                         xytext=(0, 10 if idx % 2 else -15),
                         ha='center',
                         fontsize=8,
                         arrowprops=dict(arrowstyle="->", lw=0.5))

    # 图表设置
    plt.title("OTDR曲线及事件点综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能图例
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)

    plt.tight_layout()
    plt.show()


# ---------------------------- 主程序 ----------------------------
if __name__ == "__main__":
    folder_path = "C:/Users/<USER>/Desktop/co-cable/co-cable"
    file_data = process_folder(folder_path)

    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        plot_all_curves(file_data)
        print("\n同缆检测结果（综合曲线差异和事件相似度）：")
        print("=" * 85)
        results = compare_files(file_data,
                                dtw_threshold=1000,
                                sim_threshold=0.55)
        for res in results:
            print(res)
        print("=" * 85)
        print(f"共完成 {len(file_data)} 个文件的 {len(file_data) * (len(file_data) - 1) // 2} 次比对")