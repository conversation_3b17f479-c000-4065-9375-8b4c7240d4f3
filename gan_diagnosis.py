import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from scipy import stats
from test0717 import (
    process_folder, OTDRFeatureExtractor, train_feature_extractor,
    collect_same_cable_features, set_seed, device
)

def diagnose_data_quality(file_data, feature_extractor):
    """诊断原始数据质量"""
    print("\n=== 原始数据质量诊断 ===")
    
    # 收集所有特征
    all_features = []
    group_features = {}
    
    for filename, data in file_data.items():
        from test0710 import extract_features, get_cable_group
        features = extract_features(data, feature_extractor)
        all_features.append(features)
        
        group = get_cable_group(filename)
        if group != -1:
            if group not in group_features:
                group_features[group] = []
            group_features[group].append(features)
    
    all_features = np.array(all_features)
    
    print(f"总特征数量: {len(all_features)}")
    print(f"特征维度: {all_features.shape[1]}")
    print(f"同缆组数量: {len(group_features)}")
    
    # 分析每个组的内部一致性
    print("\n各组内部一致性分析:")
    group_consistencies = []
    for group_id, features in group_features.items():
        if len(features) > 1:
            features = np.array(features)
            # 计算组内平均距离
            distances = []
            for i in range(len(features)):
                for j in range(i+1, len(features)):
                    dist = np.linalg.norm(features[i] - features[j])
                    distances.append(dist)
            
            avg_distance = np.mean(distances)
            group_consistencies.append(avg_distance)
            print(f"  组 {group_id}: {len(features)} 个样本, 平均距离: {avg_distance:.4f}")
    
    overall_consistency = np.mean(group_consistencies)
    print(f"\n整体组内一致性: {overall_consistency:.4f}")
    
    # 分析特征分布
    print("\n特征分布分析:")
    for i in range(all_features.shape[1]):
        dim_values = all_features[:, i]
        print(f"  维度 {i+1}:")
        print(f"    范围: [{np.min(dim_values):.4f}, {np.max(dim_values):.4f}]")
        print(f"    均值±标准差: {np.mean(dim_values):.4f}±{np.std(dim_values):.4f}")
        print(f"    偏度: {stats.skew(dim_values):.4f}")
        print(f"    峰度: {stats.kurtosis(dim_values):.4f}")
    
    return all_features, group_features, overall_consistency

def suggest_gan_improvements(all_features, group_features, consistency):
    """基于数据分析建议GAN改进方案"""
    print("\n=== GAN改进建议 ===")
    
    n_samples = len(all_features)
    n_groups = len(group_features)
    
    print(f"数据集大小: {n_samples} 个样本")
    print(f"同缆组数量: {n_groups} 个组")
    print(f"组内一致性: {consistency:.4f}")
    
    # 建议1：网络架构
    print("\n1. 网络架构建议:")
    if n_samples < 50:
        print("   - 数据量很小，建议使用非常简单的网络")
        print("   - 生成器: 2-3层，每层32-64个神经元")
        print("   - 判别器: 2层，每层16-32个神经元")
        print("   - 使用Dropout防止过拟合")
    elif n_samples < 200:
        print("   - 数据量较小，建议使用中等复杂度网络")
        print("   - 生成器: 3-4层，每层64-128个神经元")
        print("   - 判别器: 2-3层，每层32-64个神经元")
    else:
        print("   - 数据量充足，可以使用较复杂的网络")
    
    # 建议2：训练策略
    print("\n2. 训练策略建议:")
    if consistency > 1.0:
        print("   - 组内一致性较差，建议增加正则化")
        print("   - 使用更强的特征匹配损失")
        print("   - 考虑使用条件GAN")
    else:
        print("   - 组内一致性较好，可以使用标准训练")
    
    if n_samples < 100:
        print("   - 数据量小，建议:")
        print("     * 使用数据增强（添加噪声）")
        print("     * 降低学习率")
        print("     * 增加训练轮数")
        print("     * 使用更强的正则化")
    
    # 建议3：超参数
    print("\n3. 超参数建议:")
    suggested_latent_dim = min(64, max(16, n_samples // 4))
    suggested_batch_size = min(16, max(4, n_samples // 10))
    suggested_lr_g = 0.0002 if n_samples > 50 else 0.0001
    suggested_lr_d = suggested_lr_g / 2
    
    print(f"   - 潜在空间维度: {suggested_latent_dim}")
    print(f"   - 批次大小: {suggested_batch_size}")
    print(f"   - 生成器学习率: {suggested_lr_g}")
    print(f"   - 判别器学习率: {suggested_lr_d}")
    
    return {
        'latent_dim': suggested_latent_dim,
        'batch_size': suggested_batch_size,
        'lr_g': suggested_lr_g,
        'lr_d': suggested_lr_d,
        'use_data_aug': n_samples < 100,
        'use_strong_reg': consistency > 1.0
    }

def create_optimal_gan(suggestions):
    """根据建议创建优化的GAN"""
    print("\n=== 创建优化的GAN ===")
    
    class OptimalGenerator(nn.Module):
        def __init__(self, latent_dim, feature_dim=4):
            super(OptimalGenerator, self).__init__()
            
            if suggestions['use_strong_reg']:
                # 强正则化版本
                self.model = nn.Sequential(
                    nn.Linear(latent_dim, 64),
                    nn.BatchNorm1d(64),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.5),
                    
                    nn.Linear(64, 32),
                    nn.BatchNorm1d(32),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.3),
                    
                    nn.Linear(32, feature_dim)
                )
            else:
                # 标准版本
                self.model = nn.Sequential(
                    nn.Linear(latent_dim, 64),
                    nn.LeakyReLU(0.2),
                    
                    nn.Linear(64, 32),
                    nn.LeakyReLU(0.2),
                    
                    nn.Linear(32, feature_dim)
                )
        
        def forward(self, z):
            return self.model(z)
    
    class OptimalDiscriminator(nn.Module):
        def __init__(self, feature_dim=4):
            super(OptimalDiscriminator, self).__init__()
            
            if suggestions['use_strong_reg']:
                # 强正则化版本
                self.model = nn.Sequential(
                    nn.Linear(feature_dim, 32),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.3),
                    
                    nn.Linear(32, 16),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.3),
                    
                    nn.Linear(16, 1),
                    nn.Sigmoid()
                )
            else:
                # 标准版本
                self.model = nn.Sequential(
                    nn.Linear(feature_dim, 32),
                    nn.LeakyReLU(0.2),
                    
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
        
        def forward(self, x):
            return self.model(x)
    
    return OptimalGenerator, OptimalDiscriminator

def run_diagnosis():
    """运行完整的诊断流程"""
    print("=== GAN诊断工具 ===")
    
    # 设置随机种子
    set_seed(42)
    
    # 加载数据
    folder_path = "C:/Users/<USER>/Desktop/干线测试"
    file_data = process_folder(folder_path)
    
    if not file_data:
        print("未找到有效的OTDR文件！")
        return
    
    # 初始化特征提取器
    feature_extractor = OTDRFeatureExtractor().to(device)
    optimizer = torch.optim.Adam(feature_extractor.parameters(), lr=0.0001)
    criterion = nn.MSELoss()
    
    # 训练特征提取器
    print("训练特征提取器...")
    train_feature_extractor(file_data, feature_extractor, optimizer, criterion, epochs=50)
    
    # 诊断数据质量
    all_features, group_features, consistency = diagnose_data_quality(file_data, feature_extractor)
    
    # 获取改进建议
    suggestions = suggest_gan_improvements(all_features, group_features, consistency)
    
    # 创建优化的GAN类
    OptimalGenerator, OptimalDiscriminator = create_optimal_gan(suggestions)
    
    print(f"\n建议的GAN配置已生成！")
    print(f"可以使用以下参数训练:")
    for key, value in suggestions.items():
        print(f"  {key}: {value}")
    
    return suggestions, OptimalGenerator, OptimalDiscriminator

if __name__ == "__main__":
    run_diagnosis()
