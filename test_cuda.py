import torch
import numpy as np
import time

def test_cuda():
    print("PyTorch版本:", torch.__version__)
    print("\n=== CUDA可用性测试 ===")
    
    # 测试CUDA是否可用
    cuda_available = torch.cuda.is_available()
    print("CUDA是否可用:", cuda_available)
    
    if cuda_available:
        # 显示CUDA设备信息
        print("\n=== CUDA设备信息 ===")
        print("CUDA设备数量:", torch.cuda.device_count())
        for i in range(torch.cuda.device_count()):
            print(f"\nGPU {i}:", torch.cuda.get_device_name(i))
            print("显存总量: {:.2f}GB".format(
                torch.cuda.get_device_properties(i).total_memory / (1024**3)))
        
        # 进行简单的性能测试
        print("\n=== 性能测试 ===")
        # 创建大矩阵进行测试
        size = 5000
        
        # CPU测试
        print("\nCPU测试:")
        a_cpu = torch.randn(size, size)
        b_cpu = torch.randn(size, size)
        
        start_time = time.time()
        c_cpu = torch.matmul(a_cpu, b_cpu)
        cpu_time = time.time() - start_time
        print(f"CPU计算时间: {cpu_time:.4f} 秒")
        
        # GPU测试
        print("\nGPU测试:")
        a_gpu = a_cpu.cuda()
        b_gpu = b_cpu.cuda()
        
        # 预热GPU
        _ = torch.matmul(a_gpu, b_gpu)
        torch.cuda.synchronize()
        
        start_time = time.time()
        c_gpu = torch.matmul(a_gpu, b_gpu)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        print(f"GPU计算时间: {gpu_time:.4f} 秒")
        
        if gpu_time > 0:
            speedup = cpu_time / gpu_time
            print(f"\nGPU加速比: {speedup:.2f}x")
    
    else:
        print("\n警告：未检测到可用的CUDA设备！")
        print("可能的原因：")
        print("1. 未安装NVIDIA GPU")
        print("2. NVIDIA驱动未正确安装")
        print("3. CUDA工具包未正确安装")
        print("4. PyTorch未安装CUDA版本")

if __name__ == "__main__":
    test_cuda()
