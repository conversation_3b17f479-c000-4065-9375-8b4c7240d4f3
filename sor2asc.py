import os
import struct
from datetime import datetime


def parse_sor_header(data):
    """解析SOR文件头部信息（假设结构）"""
    header = {}

    # 示例：假设设备型号在偏移量20-40字节（需根据实际文件调整）
    header['model'] = data[20:40].decode('ascii', errors='ignore').strip()

    # 序列号在偏移量50-60字节
    header['serial'] = data[50:60].decode('ascii', errors='ignore').strip()

    # 测试时间（假设为偏移量100-104字节的Unix时间戳）
    timestamp = struct.unpack('<I', data[100:104])[0]
    header['date'] = datetime.fromtimestamp(timestamp).strftime('%Y/%m/%d')
    header['time'] = datetime.fromtimestamp(timestamp).strftime('%H:%M')

    # 波长（假设为偏移量120-124字节的浮点数）
    header['wavelength'] = struct.unpack('<f', data[120:124])[0]

    return header


def parse_sor_events(data):
    """解析事件表（需根据实际文件结构优化）"""
    # 假设事件表以特定标识符开始（如字节偏移量512）
    event_start = data.find(b'Fault Table\r\n', 512)
    if event_start == -1:
        return []

    event_data = data[event_start:event_start + 2048].decode('ascii', errors='ignore')
    events = []
    lines = [line.strip() for line in event_data.split('\n') if line.strip()]

    # 提取事件行（示例正则表达式匹配）
    import re
    pattern = re.compile(r'^\s*(\d+),([\d.]+),([^,]+),\s*([\d.-]+|\-+\s?),.*')
    for line in lines:
        match = pattern.match(line)
        if match:
            events.append({
                'No': match.group(1),
                'Loc(km)': match.group(2),
                'Event Type': match.group(3),
                'Loss(dB)': match.group(4)
            })
    return events


def parse_sor_curve(data):
    """解析曲线数据（假设从偏移量2048开始）"""
    curve_offset = 2048
    curve = []

    # 每8字节为一组（4字节距离 + 4字节损耗）
    for i in range(0, len(data) - curve_offset, 8):
        chunk = data[curve_offset + i: curve_offset + i + 8]
        if len(chunk) < 8:
            break
        distance = struct.unpack('<f', chunk[0:4])[0]
        loss = struct.unpack('<f', chunk[4:8])[0]
        curve.append((distance, loss))
    return curve


def sor_to_asc(sor_path, asc_path):
    """单个SOR文件转换"""
    with open(sor_path, 'rb') as f:
        data = f.read()

    header = parse_sor_header(data)
    events = parse_sor_events(data)
    curve = parse_sor_curve(data)

    # 生成ASC文件
    with open(asc_path, 'w') as f:
        # 写入头部
        f.write(f"EXFO_Electro_Optical_Engineering   -  OTDR_REPORT\n\n")
        f.write(f"TRACE_FILE:  {os.path.basename(asc_path)}\n")
        f.write(f"OTDR:  {header['model']}\n")
        f.write(f"SERIAL_NUMBER:  {header['serial']}\n")
        f.write(f"WAVELENGTH:  {header['wavelength']:.1f} (SM)\n")
        f.write(f"DATE:  {header['date']}\n")
        f.write(f"TIME:  {header['time']}\n\n")

        # 写入事件表
        if events:
            f.write("NUMBER_OF_AVERAGED_POINTS:  500\n\n")
            f.write("                                 FAULT TABLE\n")
            f.write("-" * 80 + "\n")
            f.write("No,Loc(km),Event Type,Loss(dB),Refl(dB),Atten(dB/km),Cumul(dB)\n")
            for event in events:
                f.write(f"{event['No']},{event['Loc(km)']},{event['Event Type']},{event['Loss(dB)']},,,...\n")

        # 写入曲线数据
        f.write("\nPOS(km)  VAL(dB)\n")
        for d, val in curve:
            f.write(f"{d:.4f} {val:.4f}\n")


def batch_convert(sor_dir, asc_dir):
    """批量转换"""
    if not os.path.exists(asc_dir):
        os.makedirs(asc_dir)

    for filename in os.listdir(sor_dir):
        if filename.lower().endswith('.sor'):
            sor_path = os.path.join(sor_dir, filename)
            asc_path = os.path.join(asc_dir, filename[:-4] + '.asc')
            try:
                sor_to_asc(sor_path, asc_path)
                print(f"Converted: {filename} -> {os.path.basename(asc_path)}")
            except Exception as e:
                print(f"Error converting {filename}: {str(e)}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='EXFO SOR to ASC Converter')
    parser.add_argument('--input', required=True, help='Input SOR file or directory')
    parser.add_argument('--output', required=True, help='Output ASC file or directory')
    args = parser.parse_args()

    if os.path.isdir(args.input):
        batch_convert(args.input, args.output)
    else:
        sor_to_asc(args.input, args.output)
        print(f"Converted: {args.input} -> {args.output}")