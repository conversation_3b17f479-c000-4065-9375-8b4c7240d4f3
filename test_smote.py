#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SMOTE数据平衡测试脚本
用于验证SMOTE功能是否正常工作
"""

import numpy as np
import matplotlib.pyplot as plt
from collections import Counter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

def test_smote_installation():
    """测试SMOTE库是否正确安装"""
    try:
        from imblearn.over_sampling import SMOTE
        print("✓ imbalanced-learn库已正确安装")
        return True
    except ImportError as e:
        print("✗ imbalanced-learn库未安装")
        print("请运行以下命令安装：")
        print("pip install imbalanced-learn")
        return False

def create_imbalanced_dataset():
    """创建一个不平衡的测试数据集"""
    np.random.seed(42)
    
    # 创建多数类样本（不同缆对）
    n_majority = 1000
    X_majority = np.random.normal(0, 1, (n_majority, 16))  # 16维特征，模拟OTDR特征
    y_majority = np.zeros(n_majority)
    
    # 创建少数类样本（同缆对）
    n_minority = 50  # 严重不平衡：20:1
    X_minority = np.random.normal(2, 1, (n_minority, 16))  # 不同的分布中心
    y_minority = np.ones(n_minority)
    
    # 合并数据
    X = np.vstack([X_majority, X_minority])
    y = np.hstack([y_majority, y_minority])
    
    return X, y

def apply_smote_test(X, y):
    """应用SMOTE并返回平衡后的数据"""
    from imblearn.over_sampling import SMOTE
    
    print("\n=== SMOTE测试 ===")
    
    # 统计原始数据分布
    original_counter = Counter(y)
    print(f"原始数据分布:")
    print(f"- 不同缆对 (0): {original_counter[0]} 个")
    print(f"- 同缆对 (1): {original_counter[1]} 个")
    print(f"- 不平衡比例: {original_counter[0]/original_counter[1]:.1f}:1")
    
    # 应用SMOTE
    k_neighbors = min(5, original_counter[1] - 1) if original_counter[1] > 1 else 1
    smote = SMOTE(random_state=42, k_neighbors=k_neighbors)
    
    X_balanced, y_balanced = smote.fit_resample(X, y)
    
    # 统计平衡后的数据分布
    balanced_counter = Counter(y_balanced)
    print(f"\nSMOTE处理后的数据分布:")
    print(f"- 不同缆对 (0): {balanced_counter[0]} 个")
    print(f"- 同缆对 (1): {balanced_counter[1]} 个")
    print(f"- 平衡比例: {balanced_counter[0]/balanced_counter[1]:.1f}:1")
    
    print(f"\n数据增强效果:")
    print(f"- 原始样本总数: {len(X)}")
    print(f"- 平衡后样本总数: {len(X_balanced)}")
    print(f"- 新增样本数: {len(X_balanced) - len(X)}")
    print(f"- 数据增长率: {((len(X_balanced) - len(X)) / len(X) * 100):.1f}%")
    
    return X_balanced, y_balanced, original_counter, balanced_counter

def visualize_results(original_counter, balanced_counter):
    """可视化SMOTE前后的数据分布"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 原始数据分布
    labels = ['不同缆对', '同缆对']
    original_counts = [original_counter[0], original_counter[1]]
    colors = ['lightcoral', 'lightblue']
    
    ax1.pie(original_counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title(f'原始数据分布\n总数: {sum(original_counts)}')
    
    # 平衡后数据分布
    balanced_counts = [balanced_counter[0], balanced_counter[1]]
    ax2.pie(balanced_counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax2.set_title(f'SMOTE平衡后数据分布\n总数: {sum(balanced_counts)}')
    
    plt.tight_layout()
    plt.show()
    
    # 柱状图对比
    fig, ax = plt.subplots(figsize=(10, 6))
    x = np.arange(len(labels))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, original_counts, width, label='原始数据', color='lightcoral', alpha=0.8)
    bars2 = ax.bar(x + width/2, balanced_counts, width, label='SMOTE平衡后', color='lightblue', alpha=0.8)
    
    ax.set_xlabel('类别')
    ax.set_ylabel('样本数量')
    ax.set_title('SMOTE前后数据分布对比')
    ax.set_xticks(x)
    ax.set_xticklabels(labels)
    ax.legend()
    
    # 在柱状图上添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
               f'{int(height)}', ha='center', va='bottom')
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
               f'{int(height)}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()

def main():
    """主测试函数"""
    print("SMOTE数据平衡功能测试")
    print("=" * 50)
    
    # 检查库安装
    if not test_smote_installation():
        return
    
    # 创建测试数据
    print("\n创建不平衡测试数据集...")
    X, y = create_imbalanced_dataset()
    
    # 应用SMOTE
    try:
        X_balanced, y_balanced, original_counter, balanced_counter = apply_smote_test(X, y)
        
        # 可视化结果
        print("\n生成可视化图表...")
        visualize_results(original_counter, balanced_counter)
        
        print("\n✓ SMOTE测试成功完成！")
        print("现在可以在主程序中使用SMOTE功能了。")
        
    except Exception as e:
        print(f"\n✗ SMOTE测试失败: {str(e)}")
        print("请检查库安装和数据格式。")

if __name__ == "__main__":
    main()
