import numpy as np
import re
import matplotlib.pyplot as plt
import os
from scipy.stats import pearsonr
from itertools import combinations
import matplotlib.pyplot as plt
from matplotlib import font_manager

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
# ===== 读取 POS(km) VAL(dB) 数据 =====
def load_trace_data(filepath):
    pos, val = [], []
    with open(filepath, 'r', encoding='ISO-8859-1', errors='ignore') as f:
        for line in f:
            line = line.strip()
            if len(line.split()) == 2:
                try:
                    p, v = map(float, line.split())
                    pos.append(p)
                    val.append(v)
                except:
                    continue
    return np.array(pos), np.array(val)


# ===== 事件特征提取 =====
def load_event_features(filename):
    events = []
    with open(filename, 'r', encoding='ISO-8859-1', errors='ignore') as file:
        lines = file.readlines()

    parsing = False
    for line in lines:
        line = line.strip()
        if "No,Loc(km),Event Type,Loss(dB),Refl(dB),Atten(dB/km),Cumul(dB)" in line:
            parsing = True
            continue

        if parsing:
            if "Fiber Section" in line or not line:
                continue

            # 处理事件行
            if re.match(r'^\s*\d+,', line):  # 以数字开头的事件行
                parts = [p.strip() for p in line.split(',')]
                try:
                    # 确保有足够的数据列
                    if len(parts) >= 7:
                        loc = float(parts[1]) if parts[1] else None
                        event_type = parts[2] if parts[2] else ""

                        # 处理特殊值如 "> -19.4" 或 "- - -"
                        loss = None
                        if parts[3] and parts[3] != '- - -':
                            if '>' in parts[3]:
                                loss = float(parts[3].replace('>', '').strip())
                            else:
                                loss = float(parts[3])

                        refl = None
                        if parts[4] and parts[4] != '- - -':
                            if '>' in parts[4]:
                                refl = float(parts[4].replace('>', '').strip())
                            else:
                                refl = float(parts[4])

                        atten = float(parts[5]) if parts[5] else None
                        cumul = float(parts[6]) if parts[6] else None

                        events.append({
                            'location': loc,
                            'type': event_type,
                            'loss': loss,
                            'reflectance': refl,
                            'attenuation': atten,
                            'cumulative': cumul
                        })
                except (ValueError, IndexError) as e:
                    print(f"解析事件行出错: {line}, 错误: {e}")
                    continue

    return events
# ===== 曲线特征提取 =====
def load_trace_data(filename):
    pos, val = [], []
    with open(filename, 'r', encoding='ISO-8859-1', errors='ignore') as f:
        for line in f:
            line = line.strip()
            if re.match(r'^\d+\.\d+\s+-?\d+\.\d+', line):
                try:
                    p, v = map(float, line.split())
                    pos.append(p)
                    val.append(v)
                except:
                    continue
    return np.array(pos), np.array(val)

# ===== 曲线相似度：皮尔逊相关系数 =====
def compute_trace_similarity(val1, val2):
    min_len = min(len(val1), len(val2))
    if min_len < 10:
        return 0.0
    val1, val2 = val1[:min_len], val2[:min_len]
    corr, _ = pearsonr(val1, val2)
    return corr if not np.isnan(corr) else 0.0

# ===== 事件点相似度 =====
def compute_event_similarity(events1, events2, distance_threshold=0.5, type_weight=0, loss_weight=0.5,
                             refl_weight=0):
    """
    改进的事件相似度计算:
    - 考虑事件类型匹配
    - 考虑位置接近度(高斯衰减)
    - 考虑损耗和反射特征的相似度
    """
    if not events1 or not events2:
        return 0.0

    matched_pairs = []

    for e1 in events1:
        best_match = None
        best_score = 0

        for e2 in events2:
            # 位置差异(使用高斯衰减函数)
            loc_diff = abs(e1['location'] - e2['location'])
            loc_sim = np.exp(-0.5 * (loc_diff / distance_threshold) ** 2) if loc_diff <= 3 * distance_threshold else 0

            # 类型相似度
            type_sim = 1.0 if e1['type'] == e2['type'] else 0.0

            # 损耗相似度(如果两者都有损耗数据)
            loss_sim = 0.0
            if e1['loss'] is not None and e2['loss'] is not None:
                loss_diff = abs(e1['loss'] - e2['loss'])
                loss_sim = 1.0 / (1.0 + loss_diff)  # 使用反比例函数

            # 反射相似度(如果两者都有反射数据)
            refl_sim = 0.0
            if e1['reflectance'] is not None and e2['reflectance'] is not None:
                refl_diff = abs(e1['reflectance'] - e2['reflectance'])
                refl_sim = 1.0 / (1.0 + refl_diff)

            # 综合评分
            score = (type_weight * type_sim +
                     (1 - type_weight) * (loss_weight * loss_sim + refl_weight * refl_sim)) * loc_sim

            if score > best_score:
                best_score = score
                best_match = e2

        if best_score > 0:
            matched_pairs.append((e1, best_match, best_score))

    # 计算总体相似度(考虑匹配对的数量和质量)
    if not matched_pairs:
        return 0.0

    avg_score = sum(score for _, _, score in matched_pairs) / len(matched_pairs)
    coverage = len(matched_pairs) / max(len(events1), len(events2))

    return avg_score * coverage


# ===== 综合相似度计算 =====
def compute_total_similarity(trace_sim, event_sim, alpha=0.8):
    return alpha * trace_sim + (1 - alpha) * event_sim

def load_trace_data(filepath):
    pos, val = [], []
    with open(filepath, 'r', encoding='ISO-8859-1', errors='ignore') as f:
        for line in f:
            line = line.strip()
            if len(line.split()) == 2:
                try:
                    p, v = map(float, line.split())
                    pos.append(p)
                    val.append(v)
                except:
                    continue
    return np.array(pos), np.array(val)
# ===== 主函数 =====
def main():
    folder = '.'  # 当前目录
    files = [f for f in os.listdir(folder) if f.endswith('.asc')]
    data = {}

    # 设置非阻塞模式
    plt.ion()  # 启用交互模式
    fig = plt.figure(figsize=(14, 7))

    # 绘制所有曲线
    for f in files:
        pos, val = load_trace_data(os.path.join(folder, f))
        if len(pos) == 0 or len(val) == 0:
            print(f"[跳过] 文件 {f} 无有效数据")
            continue
        plt.plot(pos, val, label=f, linewidth=1.5)

    plt.xlabel("POS (km)")
    plt.ylabel("VAL (dB)")
    plt.title("多个 OTDR 曲线对比图（原始数据）")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.draw()  # 更新图形但不阻塞
    plt.pause(0.001)  # 短暂暂停以确保图形更新

    # 继续执行其他任务
    for f in files:
        trace_pos, trace_val = load_trace_data(f)
        event_list = load_event_features(f)
        data[f] = {
            "trace_val": trace_val,
            "events": event_list
        }

    threshold = 0.8
    same_cable_groups = []
    visited = set()

    for f1, f2 in combinations(files, 2):
        val1 = data[f1]["trace_val"]
        val2 = data[f2]["trace_val"]
        events1 = data[f1]["events"]
        events2 = data[f2]["events"]

        trace_sim = compute_trace_similarity(val1, val2)
        event_sim = compute_event_similarity(events1, events2)
        total_sim = compute_total_similarity(trace_sim, event_sim)

        print(f"{f1} <-> {f2}: trace={trace_sim:.3f}, event={event_sim:.3f}, total={total_sim:.3f}")

        if total_sim >= threshold:
            same_cable_groups.append((f1, f2))
            visited.update([f1, f2])

    print("\n属于同缆的光纤组:")
    for pair in same_cable_groups:
        print(" - ", pair)

    # 保持图形窗口打开（可选）
    input("按 Enter 键退出...")  # 程序会在此处暂停，但图形窗口不会阻塞之前的代码执行
    plt.close(fig)  # 关闭图形

if __name__ == "__main__":
    main()