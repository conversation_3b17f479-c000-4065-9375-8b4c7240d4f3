import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
import os
from test0714 import extract_event_features, CABLE_GROUPS, process_otdr_file

class OTDRDataset(Dataset):
    """OTDR数据集类"""
    def __init__(self, file_data, cable_groups):
        self.file_data = file_data
        self.cable_groups = cable_groups
        self.pairs = self._generate_pairs()
        
    def _generate_pairs(self):
        """生成训练对：同缆为1，不同缆为0"""
        pairs = []
        files = list(self.file_data.keys())
        print(f"文件总数: {len(files)}")
        print(f"同缆组数量: {len(self.cable_groups)}")
        
        # 生成正样本（同缆对）
        positive_count = 0
        for group in self.cable_groups.values():
            for i in range(len(group)):
                for j in range(i + 1, len(group)):
                    # 从文件名中提取基本名称（不包含路径）
                    file1_base = os.path.basename(group[i])
                    file2_base = os.path.basename(group[j])
                    
                    # 在file_data中查找匹配的文件
                    file1_match = None
                    file2_match = None
                    for file_path in self.file_data.keys():
                        if file_path.endswith(file1_base):
                            file1_match = file_path
                        if file_path.endswith(file2_base):
                            file2_match = file_path
                    
                    if file1_match and file2_match:
                        pairs.append((file1_match, file2_match, 1))
                        positive_count += 1
        
        print(f"生成的正样本数量: {positive_count}")
        
        # 生成负样本（不同缆对）
        negative_pairs = []
        for i in range(len(files)):
            for j in range(i + 1, len(files)):
                file1, file2 = files[i], files[j]
                file1_base = os.path.basename(file1)
                file2_base = os.path.basename(file2)
                
                # 检查是否属于不同组
                is_same_group = False
                for group in self.cable_groups.values():
                    if (file1_base in group and file2_base in group):
                        is_same_group = True
                        break
                
                if not is_same_group:
                    negative_pairs.append((file1, file2, 0))
        
        print(f"生成的负样本数量（未平衡）: {len(negative_pairs)}")
        
        # 平衡正负样本
        if positive_count > 0:
            target_neg_count = positive_count  # 让负样本数量等于正样本
            np.random.shuffle(negative_pairs)
            negative_pairs = negative_pairs[:target_neg_count]
            pairs.extend(negative_pairs)
            print(f"最终使用的负样本数量: {len(negative_pairs)}")
        else:
            print("警告：没有生成任何正样本！生成随机样本对进行测试...")
            # 如果没有正样本，则生成一些随机样本对
            for i in range(len(files)):
                for j in range(i + 1, len(files)):
                    pairs.append((files[i], files[j], 0))
                    if len(pairs) >= 100:  # 限制样本数量
                        break
                if len(pairs) >= 100:
                    break
            print(f"生成了 {len(pairs)} 个随机样本对")
        
        if not pairs:
            raise ValueError("无法生成任何样本对！请检查输入数据。")
        
        print(f"最终生成的样本对总数: {len(pairs)}")
        return pairs
    
    def _get_group(self, filename):
        """获取文件所属的组"""
        for group_id, files in self.cable_groups.items():
            if filename in files:
                return group_id
        return -1
    
    def _extract_features(self, curve_data):
        """从OTDR曲线数据提取特征"""
        try:
            # 1. 基本统计特征
            mean = np.mean(curve_data[:, 1])
            std = np.std(curve_data[:, 1])
            max_val = np.max(curve_data[:, 1])
            min_val = np.min(curve_data[:, 1])
            
            # 2. 斜率特征
            slopes = np.diff(curve_data[:, 1]) / np.diff(curve_data[:, 0])
            mean_slope = np.mean(slopes)
            std_slope = np.std(slopes)
            
            # 3. 事件特征（直接从曲线数据计算）
            # 寻找显著变化点作为事件
            diff_threshold = 2 * np.std(slopes)  # 使用斜率标准差的2倍作为阈值
            event_indices = np.where(np.abs(slopes) > diff_threshold)[0]
            
            # 合并相邻的事件点
            if len(event_indices) > 0:
                merged_events = [event_indices[0]]
                for i in range(1, len(event_indices)):
                    if event_indices[i] - merged_events[-1] > 5:  # 如果间隔超过5个点，认为是新事件
                        merged_events.append(event_indices[i])
            else:
                merged_events = []
            
            n_events = len(merged_events)
            
            # 计算事件位置（距离）
            event_positions = [curve_data[idx, 0] for idx in merged_events] if merged_events else [0]
            
            # 计算事件损耗（幅度变化）
            event_losses = []
            for idx in merged_events:
                if idx > 0 and idx + 1 < len(curve_data):
                    # 计算事件点前后的幅度差
                    loss = abs(curve_data[idx + 1, 1] - curve_data[idx - 1, 1])
                    event_losses.append(loss)
            if not event_losses:
                event_losses = [0]
            
            # 根据损耗大小判断事件类型
            event_types = []
            for loss in event_losses:
                if loss < 0.5:  # 小损耗
                    event_types.append(1)  # Non-Reflective
                elif loss < 2.0:  # 中等损耗
                    event_types.append(2)  # Reflective
                else:  # 大损耗
                    event_types.append(3)  # Positive
            if not event_types:
                event_types = [0]
            
            # 组合所有特征
            features = np.array([
                mean, std, max_val, min_val,
                mean_slope, std_slope,
                n_events,
                np.mean(event_positions),
                np.mean(event_losses),
                np.mean(event_types)
            ])
            
            return features.astype(np.float32)
            
        except Exception as e:
            print(f"特征提取错误: {str(e)}")
            # 返回默认特征向量
            return np.zeros(10, dtype=np.float32)
    
    def __len__(self):
        return len(self.pairs)
    
    def __getitem__(self, idx):
        file1, file2, label = self.pairs[idx]
        
        # 获取两条曲线的特征
        features1 = self._extract_features(self.file_data[file1])
        features2 = self._extract_features(self.file_data[file2])
        
        return (
            torch.from_numpy(features1),
            torch.from_numpy(features2),
            torch.tensor(label, dtype=torch.float32)
        )

class SiameseNetwork(nn.Module):
    """孪生神经网络模型"""
    def __init__(self, input_size):
        super(SiameseNetwork, self).__init__()
        
        self.encoder = nn.Sequential(
            nn.Linear(input_size, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32)
        )
        
        self.fc = nn.Sequential(
            nn.Linear(32 * 2, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
    
    def forward_one(self, x):
        return self.encoder(x)
    
    def forward(self, x1, x2):
        out1 = self.forward_one(x1)
        out2 = self.forward_one(x2)
        # 连接两个特征向量
        combined = torch.cat((out1, out2), 1)
        # 通过全连接层得到相似度
        similarity = self.fc(combined)
        return similarity

def train_model(model, train_loader, val_loader, num_epochs=50):
    """训练模型"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    best_val_loss = float('inf')
    patience = 5
    counter = 0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        for batch_idx, (data1, data2, target) in enumerate(train_loader):
            data1, data2, target = data1.to(device), data2.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data1, data2)
            loss = criterion(output.squeeze(), target)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data1, data2, target in val_loader:
                data1, data2, target = data1.to(device), data2.to(device), target.to(device)
                output = model(data1, data2)
                val_loss += criterion(output.squeeze(), target).item()
                
                pred = (output.squeeze() > 0.5).float()
                correct += (pred == target).sum().item()
                total += target.size(0)
        
        val_loss /= len(val_loader)
        accuracy = correct / total
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'Training Loss: {train_loss:.4f}')
        print(f'Validation Loss: {val_loss:.4f}')
        print(f'Validation Accuracy: {accuracy:.4f}')
        
        # 早停
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_model.pth')
        else:
            counter += 1
            if counter >= patience:
                print("Early stopping triggered")
                break

def predict_co_cable(model, file_data, threshold=0.5):
    """预测同缆组"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    model.eval()
    
    files = list(file_data.keys())
    n_files = len(files)
    similarity_matrix = np.zeros((n_files, n_files))
    
    # 构建特征提取器
    dataset = OTDRDataset(file_data, {})  # 空的cable_groups
    
    with torch.no_grad():
        for i in range(n_files):
            for j in range(i+1, n_files):
                features1 = dataset._extract_features(file_data[files[i]])
                features2 = dataset._extract_features(file_data[files[j]])
                
                features1 = torch.from_numpy(features1).to(device)
                features2 = torch.from_numpy(features2).to(device)
                
                similarity = model(features1.unsqueeze(0), features2.unsqueeze(0))
                similarity_matrix[i,j] = similarity.item()
                similarity_matrix[j,i] = similarity.item()
    
    # 使用相似度矩阵进行聚类
    from sklearn.cluster import DBSCAN
    clustering = DBSCAN(eps=threshold, min_samples=2, metric='precomputed')
    distances = 1 - similarity_matrix
    labels = clustering.fit_predict(distances)
    
    # 整理结果
    groups = {}
    for i, label in enumerate(labels):
        if label == -1:  # 噪声点
            continue
        if label not in groups:
            groups[label] = []
        groups[label].append(files[i])
    
    return groups

def main():
    """主函数"""
    print("开始加载数据...")
    folder_path = "C:/Users/<USER>/Desktop/干线测试"
    file_data = {}
    
    # 遍历文件夹获取所有.asc文件
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.asc'):
                file_path = os.path.join(root, file)
                try:
                    data = process_otdr_file(file_path)
                    if len(data) > 0:
                        # 使用文件的相对路径作为键
                        rel_path = os.path.relpath(file_path, folder_path)
                        file_data[rel_path] = data
                except Exception as e:
                    print(f"处理文件失败: {file}, 错误: {str(e)}")
    
    print(f"成功加载 {len(file_data)} 个OTDR文件")
    print("文件列表:")
    for file in file_data.keys():
        print(f"  {file}")
    
    print("\n已知同缆组:")
    for group_id, files in CABLE_GROUPS.items():
        print(f"\n组 {group_id}:")
        for file in files:
            found = False
            for data_file in file_data.keys():
                if data_file.endswith(file):
                    print(f"  {file} (已加载: {data_file})")
                    found = True
                    break
            if not found:
                print(f"  {file} (未找到)")
    
    # 创建数据集
    try:
        dataset = OTDRDataset(file_data, CABLE_GROUPS)
        
        if len(dataset) < 2:
            print("错误：数据集样本数量太少，无法进行训练！")
            return
        
        # 划分训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
        
        batch_size = min(32, len(train_dataset))
        if batch_size == 0:
            raise ValueError("训练集为空！")
            
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=min(32, len(val_dataset)))
        
        # 创建模型
        input_size = 10  # 特征维度
        model = SiameseNetwork(input_size)
        
        # 训练模型
        print("开始训练模型...")
        train_model(model, train_loader, val_loader)
        
        # 加载最佳模型
        model.load_state_dict(torch.load('best_model.pth'))
        
        # 预测同缆组
        print("预测同缆组...")
        predicted_groups = predict_co_cable(model, file_data)
        
        # 输出结果
        print("\n预测的同缆组：")
        for group_id, files in predicted_groups.items():
            print(f"\n组 {group_id + 1}:")
            for file in files:
                print(f"  {file}")
                
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 