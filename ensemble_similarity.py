import os
import re
import numpy as np
from scipy.spatial.distance import euclidean, cosine
from scipy.stats import skew, kurtosis
from scipy.signal import find_peaks
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from itertools import combinations
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from datetime import datetime
import json
from collections import defaultdict

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前10个点和尾端50个点
    if len(data) > 60:
        data = data[10:-50]

    return data

def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            data = process_otdr_file(file_path)
            if len(data) > 0:
                file_dict[filename] = data
    return file_dict

def calculate_cosine_similarity(data1, data2):
    """计算余弦相似度
    
    使用0填充方法处理不等长数据：
    1. 将较短的序列用0填充到与最长序列相同的长度
    2. 保持原始数据点的分布
    3. 考虑信号强度特征
    """
    # 获取两个序列的y值
    y1 = data1[:, 1]
    y2 = data2[:, 1]
    
    # 获取最大长度
    max_len = max(len(y1), len(y2))
    
    # 用0填充较短的序列
    if len(y1) < max_len:
        y1_padded = np.pad(y1, (0, max_len - len(y1)), 'constant', constant_values=0)
    else:
        y1_padded = y1
        
    if len(y2) < max_len:
        y2_padded = np.pad(y2, (0, max_len - len(y2)), 'constant', constant_values=0)
    else:
        y2_padded = y2
    
    # 对填充后的数据进行归一化处理
    y1_norm = (y1_padded - np.mean(y1)) / (np.std(y1) + 1e-8)  # 只用原始数据的均值和标准差
    y2_norm = (y2_padded - np.mean(y2)) / (np.std(y2) + 1e-8)
    
    # 计算余弦相似度
    similarity = 1 - cosine(y1_norm, y2_norm)
    
    # 添加长度差异惩罚项
    length_ratio = min(len(y1), len(y2)) / max_len
    
    # 计算最终相似度（考虑长度差异）
    final_similarity = similarity
    
    return max(0.0, min(1.0, final_similarity))  # 确保结果在[0,1]范围内

def calculate_euclidean_distance(data1, data2):
    """计算欧几里得距离（使用0填充对齐）
    
    参数:
    data1, data2: 包含 [position, value] 的numpy数组
    
    返回:
    float: 归一化的欧几里德距离
    """
    # 获取两个序列的y值
    y1 = data1[:, 1]
    y2 = data2[:, 1]
    
    # 获取最大长度
    max_len = max(len(y1), len(y2))
    
    # 用0填充较短的序列
    if len(y1) < max_len:
        y1_padded = np.pad(y1, (0, max_len - len(y1)), 'constant', constant_values=0)
    else:
        y1_padded = y1
        
    if len(y2) < max_len:
        y2_padded = np.pad(y2, (0, max_len - len(y2)), 'constant', constant_values=0)
    else:
        y2_padded = y2
    
    # 对填充后的数据进行归一化处理
    y1_norm = (y1_padded - np.mean(y1)) / (np.std(y1) + 1e-8)  # 只用原始数据的均值和标准差
    y2_norm = (y2_padded - np.mean(y2)) / (np.std(y2) + 1e-8)
    
    # 计算归一化的欧几里德距离
    distance = np.linalg.norm(y1_norm - y2_norm) / np.sqrt(max_len)
    
    return distance

def calculate_dtw_distance(data1, data2):
    """计算平均DTW距离（DTW距离除以最短路径步长）
    
    参数:
    data1, data2: 包含 [position, value] 的numpy数组
    
    返回:
    float: 平均DTW距离（总距离/路径步长）
    """
    distance, path = fastdtw(data1, data2, dist=euclidean)
    # 使用路径长度（步数）来计算平均距离
    path_length = len(path)
    return distance / path_length

def ensemble_similarity(file_dict, threshold_cs=0.95, threshold_ed=0.1, threshold_dtw=5):
    """
    集成学习方法比较曲线相似度
    
    参数:
    file_dict: 包含所有文件数据的字典
    threshold_cs: 余弦相似度阈值 (越大越相似，范围[0,1])
    threshold_ed: 欧氏距离阈值 (越小越相似，建议范围[0.1,0.5])
    threshold_dtw: DTW平均距离阈值 (越小越相似，建议范围[1,10])
    """
    filenames = list(file_dict.keys())
    results = []
    
    # 初始化归一化器
    scaler = MinMaxScaler()
    
    for (file1, file2) in combinations(filenames, 2):
        data1 = file_dict[file1]
        data2 = file_dict[file2]
        
        # 计算三种相似度指标
        cs = calculate_cosine_similarity(data1, data2)
        ed = calculate_euclidean_distance(data1, data2)
        dtw = calculate_dtw_distance(data1, data2)
        
        # 投票决策
        votes = []
        votes.append(1 if cs >= threshold_cs else 0)  # 余弦相似度投票
        votes.append(1 if ed <= threshold_ed else 0)  # 欧氏距离投票
        votes.append(1 if dtw <= threshold_dtw else 0)  # DTW距离投票
        
        # 计算综合得分 (简单多数投票)
        ensemble_result = "同缆" if sum(votes) >= 2 else "不同缆"
        
        # 存储结果
        results.append({
            'file1': file1,
            'file2': file2,
            'cosine_similarity': cs,
            'euclidean_distance': ed,
            'dtw_distance': dtw,
            'votes': sum(votes),
            'conclusion': ensemble_result
        })
    
    return results

def plot_all_curves(file_dict):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    for idx, filename in enumerate(filenames):
        data = file_dict[filename]
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    plt.show()

def find_ungrouped_files(file_dict, same_cable_groups):
    """找出未被分组的文件"""
    all_files = set(file_dict.keys())
    grouped_files = set()
    for group in same_cable_groups:
        grouped_files.update(group)
    return sorted(list(all_files - grouped_files))

def plot_cable_groups(file_dict, same_cable_groups):
    """为每个同缆组绘制曲线对比图"""
    # 首先绘制同缆组
    for idx, group in enumerate(same_cable_groups, 1):
        plt.figure(figsize=(15, 8))
        
        # 使用不同的颜色
        colors = plt.cm.rainbow(np.linspace(0, 1, len(group)))
        
        # 绘制该组的所有曲线
        for filename, color in zip(sorted(group), colors):
            data = file_dict[filename]
            plt.plot(data[:, 0], data[:, 1], 
                    label=filename, 
                    color=color,
                    linewidth=2,
                    alpha=0.8)
        
        plt.title(f"第 {idx} 组同缆曲线对比（共{len(group)}条曲线）", fontsize=14, pad=20)
        plt.xlabel("距离 (km)", fontsize=12)
        plt.ylabel("衰减 (dB)", fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 调整图例位置和大小
        plt.legend(bbox_to_anchor=(1.05, 1), 
                  loc='upper left', 
                  fontsize=10)
        
        plt.tight_layout()
        plt.show()
    
    # 找出未分组的文件并绘制
    ungrouped_files = find_ungrouped_files(file_dict, same_cable_groups)
    if ungrouped_files:
        plt.figure(figsize=(15, 8))
        colors = plt.cm.rainbow(np.linspace(0, 1, len(ungrouped_files)))
        
        for filename, color in zip(ungrouped_files, colors):
            data = file_dict[filename]
            plt.plot(data[:, 0], data[:, 1], 
                    label=filename, 
                    color=color,
                    linewidth=2,
                    alpha=0.8)
        
        plt.title(f"未分组的独立曲线（共{len(ungrouped_files)}条曲线）", fontsize=14, pad=20)
        plt.xlabel("距离 (km)", fontsize=12)
        plt.ylabel("衰减 (dB)", fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend(bbox_to_anchor=(1.05, 1), 
                  loc='upper left', 
                  fontsize=10)
        plt.tight_layout()
        plt.show()

def save_results_to_file(results, folder_path):
    """将同缆结果保存到txt文件中"""
    # 创建输出文件名（使用当前时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = os.path.join(folder_path, f"同缆分析结果_{timestamp}.txt")
    
    # 统计同缆组
    same_cable_groups = []
    processed_pairs = set()
    
    # 首先找出所有同缆对
    same_cable_pairs = [(r['file1'], r['file2']) for r in results if r['conclusion'] == "同缆"]
    
    # 合并同缆组
    for file1, file2 in same_cable_pairs:
        # 检查是否已处理过这对文件
        if file1 in processed_pairs or file2 in processed_pairs:
            # 查找现有组并合并
            for group in same_cable_groups:
                if file1 in group or file2 in group:
                    group.add(file1)
                    group.add(file2)
                    break
        else:
            # 创建新组
            same_cable_groups.append({file1, file2})
        
        processed_pairs.add(file1)
        processed_pairs.add(file2)
    
    # 合并重叠的组
    i = 0
    while i < len(same_cable_groups):
        j = i + 1
        while j < len(same_cable_groups):
            if same_cable_groups[i] & same_cable_groups[j]:  # 如果两组有交集
                same_cable_groups[i] = same_cable_groups[i].union(same_cable_groups[j])
                same_cable_groups.pop(j)
            else:
                j += 1
        i += 1
    
    # 写入文件
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write(f"OTDR曲线同缆分析结果\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据来源: {folder_path}\n")
        f.write("=" * 80 + "\n\n")
        
        # 写入同缆组信息
        f.write(f"发现 {len(same_cable_groups)} 组同缆:\n")
        for idx, group in enumerate(same_cable_groups, 1):
            f.write(f"\n第 {idx} 组同缆文件:\n")
            for filename in sorted(group):
                f.write(f"  - {filename}\n")
            f.write("-" * 40 + "\n")
        
        # 写入未分组文件信息
        ungrouped_files = find_ungrouped_files({r['file1']: None for r in results}, same_cable_groups)
        if ungrouped_files:
            f.write("\n未分组的独立文件:\n")
            for filename in ungrouped_files:
                f.write(f"  - {filename}\n")
            f.write("-" * 40 + "\n")
        
        # 写入详细的相似度数据
        f.write("\n详细相似度数据:\n")
        f.write("=" * 80 + "\n")
        for result in results:
            if result['conclusion'] == "同缆":
                f.write(f"\n{result['file1']} vs {result['file2']}\n")
                f.write(f"余弦相似度: {result['cosine_similarity']:.4f}\n")
                f.write(f"欧氏距离: {result['euclidean_distance']:.4f}\n")
                f.write(f"DTW距离: {result['dtw_distance']:.4f}\n")
                f.write(f"投票数: {result['votes']}/3\n")
                f.write("-" * 40 + "\n")
    
    return output_filename, same_cable_groups

if __name__ == "__main__":
    folder_path = "C:/Users/<USER>/Desktop/干线测试/ShundeYongfeng_GuangzhouQisuo_48F"       
    file_data = process_folder(folder_path)

    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        # 绘制所有曲线
        plot_all_curves(file_data)
        
        # 进行集成学习比较
        print("\n集成学习曲线相似度分析结果：")
        print("=" * 100)
        results = ensemble_similarity(file_data)
        
        # 保存结果到文件并获取分组信息
        output_file, same_cable_groups = save_results_to_file(results, folder_path)
        
        # 找出未分组的文件
        ungrouped_files = find_ungrouped_files(file_data, same_cable_groups)
        
        # 输出分组结果到控制台
        print(f"\n发现 {len(same_cable_groups)} 组同缆:")
        for idx, group in enumerate(same_cable_groups, 1):
            print(f"\n第 {idx} 组同缆文件:")
            for filename in sorted(group):
                print(f"  - {filename}")
            print("-" * 40)
        
        # 输出未分组文件
        if ungrouped_files:
            print("\n未分组的独立文件:")
            for filename in ungrouped_files:
                print(f"  - {filename}")
            print("-" * 40)
        
        # 绘制每个同缆组的曲线对比图
        print("\n正在生成曲线对比图...")
        plot_cable_groups(file_data, same_cable_groups)
        
        print("\n" + "=" * 100)
        print(f"同缆分析结果已保存至: {output_file}")
        print(f"共完成 {len(file_data)} 个文件的 {len(file_data) * (len(file_data) - 1) // 2} 次比对") 