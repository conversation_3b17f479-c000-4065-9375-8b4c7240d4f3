import os
os.environ['KMP_DUPLICATE_LIB_OK']='True'  # 解决OpenMP冲突问题

import re
import numpy as np
from scipy.spatial.distance import euclidean
from fastdtw import fastdtw
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from itertools import combinations
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim
import time  # 添加时间模块导入

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class OTDRDataset(Dataset):
    def __init__(self, curves_pairs, labels):  
        self.curves_pairs = curves_pairs  # 每个元素是一对OTDR曲线数据
        self.labels = labels  # 1表示同缆，0表示不同缆
        
    def __len__(self):
        return len(self.labels)     
    
    def __getitem__(self, idx): # 获取数据集中的第idx个样本
        curve1, curve2 = self.curves_pairs[idx]
        # 将数据转换为张量，并保持2D形状 (长度, 特征数=2)
        curve1_tensor = torch.FloatTensor(curve1)
        curve2_tensor = torch.FloatTensor(curve2)
        label = torch.FloatTensor([self.labels[idx]])
        return curve1_tensor, curve2_tensor, label

class OTDRNet(nn.Module):
    def __init__(self, input_size=2, hidden_size=128):
        super(OTDRNet, self).__init__()
        self.hidden_size = hidden_size
        
        # 特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_size, 32),
            nn.ReLU(),  
            nn.Linear(32, 64),
            nn.ReLU(),
        )
        
        # LSTM层处理不同长度的序列
        self.lstm = nn.LSTM(input_size=64, 
                           hidden_size=hidden_size,
                           num_layers=2,
                           batch_first=True,
                           bidirectional=True,
                           dropout=0.2)
        
        # 注意力层
        self.attention = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1)
        )
        
        # 特征融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_size * 4, hidden_size * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
        )
        
        # 相似度计算层
        self.similarity = nn.Sequential(
            nn.Linear(hidden_size, 32),    # 第一个全连接层：从hidden_size(默认128)维降到32维
            nn.ReLU(),                     # 非线性激活函数，增加模型表达能力
            nn.Dropout(0.2),               # 随机丢弃20%的神经元，防止过拟合
            nn.Linear(32, 1),              # 第二个全连接层：从32维降到1维，得到单一输出值
            nn.Sigmoid()                   # Sigmoid激活函数，将输出压缩到0-1之间
        )
        
    def attention_net(self, lstm_output):
        # lstm_output shape: (batch, seq_len, hidden_size * 2)
        attn_weights = self.attention(lstm_output)  # (batch, seq_len, 1)
        attn_weights = F.softmax(attn_weights, dim=1)
        
        # 加权求和
        context = torch.bmm(lstm_output.transpose(1, 2), attn_weights)  # (batch, hidden_size * 2, 1)
        return context.squeeze(2)  # (batch, hidden_size * 2)
        
    def forward_single(self, x):
        # x shape: (batch, seq_len, input_size)
        batch_size, seq_len, _ = x.shape
        
        # 对每个时间步进行特征提取
        x = x.reshape(-1, x.size(-1))  # (batch * seq_len, input_size)
        x = self.feature_extractor(x)
        x = x.reshape(batch_size, seq_len, -1)  # (batch, seq_len, 64)
        
        # LSTM处理序列
        lstm_out, _ = self.lstm(x)  # (batch, seq_len, hidden_size * 2)
        
        # 使用注意力机制
        attn_out = self.attention_net(lstm_out)  # (batch, hidden_size * 2)
        
        # 全局平均池化和最大池化
        avg_pool = torch.mean(lstm_out, dim=1)  # (batch, hidden_size * 2)
        max_pool, _ = torch.max(lstm_out, dim=1)  # (batch, hidden_size * 2)
        
        # 合并特征
        pooled = torch.cat([attn_out, avg_pool], dim=1)  # (batch, hidden_size * 4)
        return pooled
    
    def forward(self, x1, x2):
        # 分别处理两条曲线
        feat1 = self.forward_single(x1)  # (batch, hidden_size * 4)
        feat2 = self.forward_single(x2)  # (batch, hidden_size * 4)
        
        # 计算特征差异
        diff = torch.abs(feat1 - feat2)  # (batch, hidden_size * 4)
        
        # 特征融合
        fused = self.fusion(diff)  # (batch, hidden_size)
        
        # 计算相似度
        similarity = self.similarity(fused)  # (batch, 1)
        return similarity

def collate_fn(batch):
    """自定义批处理函数，处理不同长度的序列"""
    curves1, curves2, labels = zip(*batch)
    
    # 转换为张量
    labels = torch.stack(labels)
    
    # 直接返回列表，不进行填充
    return curves1, curves2, labels

def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    try:
        # 首先尝试 utf-8 编码
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            # 如果 utf-8 失败，尝试 gbk 编码
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                # 如果 gbk 也失败，尝试 latin-1 编码
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
            except Exception as e:
                print(f"无法读取文件 {file_path}: {str(e)}")
                return np.array([])

    # 提取光纤长度
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):  # 匹配数字格式
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        try:
            pos, val = map(float, line.strip().split())
            data.append([pos, val])
        except ValueError:
            continue  # 跳过无法转换为数值的行

    data = np.array(data)

    # 如果没有有效数据，返回空数组
    if len(data) == 0:
        print(f"警告：文件 {file_path} 中没有找到有效数据")
        return np.array([])

    # 去除首端前10个点
    if len(data) > 10:
        data = data[10:]

    # 去除尾端50个点
    if len(data) > 40:
        data = data[:-40]

    # 去除超过光纤长度的尾端
    if link_length and len(data) > 0:
        data = data[data[:, 0] <= link_length]

    return data

def process_folder(folder_path):
    """递归处理文件夹及其子文件夹中的OTDR文件"""
    file_dict = {}
    folder_index = {}  # 用于存储文件夹索引

    def get_file_number(filename):
        """从文件名中提取数字"""
        # 移除扩展名
        name = os.path.splitext(filename)[0]
        # 提取数字部分
        numbers = ''.join(c for c in name if c.isdigit())
        return int(numbers) if numbers else 0

    def process_subfolder(current_path, relative_path=''):
        # 只获取.asc文件
        files = [f for f in os.listdir(current_path) if f.endswith(".asc")]
        
        # 如果当前文件夹包含.asc文件，将其作为一个独立的文件夹处理
        if files:
            current_folder = os.path.basename(current_path)
            if current_folder not in folder_index:
                folder_index[current_folder] = len(folder_index) + 1
            
            folder_idx = folder_index[current_folder]
            
            # 处理当前文件夹中的所有.asc文件
            for filename in files:
                file_path = os.path.join(current_path, filename)
                data = process_otdr_file(file_path)
                
                if len(data) > 0:
                    # 使用文件名中的数字作为文件编号
                    file_number = get_file_number(filename)
                    simple_id = f"{folder_idx}-{file_number}"
                    file_dict[simple_id] = {
                        'data': data,
                        'original_name': filename,
                        'full_path': file_path,
                        'folder_name': current_folder
                    }
        
        # 处理子文件夹
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)
            if os.path.isdir(item_path):
                new_relative_path = os.path.join(relative_path, item)
                process_subfolder(item_path, new_relative_path)

    # 开始递归处理
    process_subfolder(folder_path)
    
    if not file_dict:
        print("\n警告：未找到任何.asc文件！")
        return file_dict
    
    # 打印文件夹映射信息
    print("\n文件夹编号对应关系：")
    for folder, idx in sorted(folder_index.items(), key=lambda x: x[1]):
        print(f"文件夹 {idx}: {folder}")
        
    # 打印找到的文件
    print("\n找到的.asc文件：")
    for file_id, file_info in sorted(file_dict.items()):
        print(f"{file_id}: {file_info['original_name']}")
    
    return file_dict

def plot_all_curves(file_dict, ref_file=None, save_path='otdr_curves.png'):
    """绘制所有曲线在同一图中并保存为图片文件"""
    plt.figure(figsize=(12, 6))
    filenames = list(file_dict.keys())

    # 生成不同颜色
    colors = ListedColormap(['blue', 'green', 'red', 'purple', 'orange', 'brown']).colors

    # 绘制所有曲线
    for idx, filename in enumerate(filenames):
        data = file_dict[filename]['data']  # 修改这里以适应新的数据结构
        color = colors[idx % len(colors)]
        plt.plot(data[:, 0], data[:, 1],
                 color=color,
                 linewidth=1.5,
                 alpha=0.7,
                 label=f'{filename} (长度: {data[-1, 0]:.2f}km)')

    # 设置图表属性
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)

    # 智能调整图例位置
    leg = plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    for line in leg.get_lines():
        line.set_linewidth(3)
    plt.tight_layout()
    
    # 保存图片到文件
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形，释放内存
    print(f"图形已保存至: {save_path}")

def prepare_training_data(file_dict, threshold=1000):
    """准备神经网络训练数据，使用固定的DTW距离阈值1000"""
    curves_pairs = []
    labels = []
    distances = []
    
    for (file1, file2) in combinations(list(file_dict.keys()), 2):
        data1 = file_dict[file1]['data']
        data2 = file_dict[file2]['data']
        distance, _ = fastdtw(data1, data2, dist=euclidean)
        distances.append((distance, data1, data2))
    
    print(f"\n使用固定DTW距离阈值: {threshold}")
    
    for distance, data1, data2 in distances:
        curves_pairs.append((data1, data2))
        labels.append(1.0 if distance <= threshold else 0.0)
    
    return curves_pairs, labels

def train_model(model, train_loader, criterion, optimizer, device, num_epochs=7):
    """训练模型"""
    model.train()
    best_accuracy = 0
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=2, verbose=True
    )
    
    for epoch in range(num_epochs):
        total_loss = 0
        correct = 0
        total = 0
        
        for curves1, curves2, labels in train_loader:
            batch_curves1 = [curve.to(device) for curve in curves1]
            batch_curves2 = [curve.to(device) for curve in curves2]
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            batch_size = len(batch_curves1)
            outputs = torch.zeros(batch_size, 1).to(device)
            
            for i in range(batch_size):
                output = model(batch_curves1[i].unsqueeze(0), batch_curves2[i].unsqueeze(0))
                outputs[i] = output
            
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            predicted = (outputs > 0.5).float()#二分类阈值0.5
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        epoch_loss = total_loss / len(train_loader)
        accuracy = 100 * correct / total
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Accuracy: {accuracy:.2f}%')
        
        # 更新学习率
        scheduler.step(accuracy)
        
        # 保存最佳模型
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'accuracy': accuracy,
            }, 'best_otdr_model.pth')
            print(f'保存新的最佳模型，准确率: {accuracy:.2f}%')

def predict_similarity(model, curve1, curve2, device):
    """预测两条曲线的相似度"""
    model.eval()
    with torch.no_grad():
        # 转换为张量并移到设备
        curve1_tensor = torch.FloatTensor(curve1).unsqueeze(0).to(device)  # 添加批次维度
        curve2_tensor = torch.FloatTensor(curve2).unsqueeze(0).to(device)  # 添加批次维度
        
        output = model(curve1_tensor, curve2_tensor)
        return output.item()

def neural_network_compare(file_dict, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
    """使用训练好的神经网络进行比较，并按同缆分组显示结果"""
    filenames = list(file_dict.keys())
    results = []
    dtw_distances = []  # 存储DTW距离
    threshold = 1000  # DTW距离阈值
    
    print("\n计算相似度和DTW距离...")
    for (file1, file2) in combinations(filenames, 2):
        data1 = file_dict[file1]['data']
        data2 = file_dict[file2]['data']
        
        # 计算DTW距离（用于参考）
        distance, _ = fastdtw(data1, data2, dist=euclidean)
        dtw_distances.append((file1, file2, distance))
        
        # 使用神经网络预测相似度
        similarity = predict_similarity(model, data1, data2, device)
        
        # 存储结果
        results.append((file1, file2, similarity))
    
    # 按相似度降序排序
    results.sort(key=lambda x: x[2], reverse=True)
    
    # 创建同缆分组（使用神经网络预测结果）
    groups = []  # 使用列表存储每个分组的文件集合
    used_files = set()
    
    # 使用神经网络预测结果进行分组
    for file1, file2, similarity in results:
        if similarity > 0.5:  # 神经网络预测阈值
            # 查找现有组
            matching_groups = []
            for group in groups:
                if file1 in group or file2 in group:
                    matching_groups.append(group)
            
            if not matching_groups:
                # 如果没有匹配的组，创建新组
                groups.append({file1, file2})
            else:
                # 合并所有匹配的组
                new_group = set()
                for group in matching_groups:
                    new_group.update(group)
                    groups.remove(group)
                new_group.add(file1)
                new_group.add(file2)
                groups.append(new_group)
            
            used_files.update([file1, file2])
    
    # 将未分组的文件单独成组
    for file in filenames:
        if file not in used_files:
            groups.append({file})
    
    # 格式化输出结果
    formatted_results = []
    formatted_results.append("\n=== 同缆分组结果（基于神经网络预测）===")
    for idx, group in enumerate(groups, 1):
        formatted_results.append(f"\n分组 {idx}:")
        files_list = sorted(list(group))
        formatted_results.append("  " + ", ".join(files_list))
    
    formatted_results.append("\n=== 详细分析结果 ===")
    for file1, file2, similarity in results:
        # 找到对应的DTW距离
        dtw_distance = next(d[2] for d in dtw_distances if d[0] == file1 and d[1] == file2)
        nn_conclusion = "同缆" if similarity > 0.5 else "不同缆"
        dtw_conclusion = "同缆" if dtw_distance <= threshold else "不同缆"
        formatted_results.append(
            f"{file1} vs {file2} | 神经网络相似度: {similarity:.4f} | DTW距离: {dtw_distance:.1f} | "
            f"神经网络结论: {nn_conclusion} | DTW结论: {dtw_conclusion}"
        )
    
    return formatted_results

def generate_analysis_report(file_dict, results, save_path='otdr_analysis_report.txt'):
    """生成分析报告并保存到文件"""
    with open(save_path, 'w', encoding='utf-8') as f:
        # 写入标题
        f.write("=" * 80 + "\n")
        f.write("OTDR曲线分析报告\n")
        f.write("=" * 80 + "\n\n")

        # 写入文件信息统计
        f.write("1. 文件统计信息\n")
        f.write("-" * 40 + "\n")
        f.write(f"总计分析文件数: {len(file_dict)}\n")
        
        # 按文件夹统计文件数量
        folder_stats = {}
        for file_path in file_dict.keys():
            folder = file_path.split('-')[0]  # 获取文件夹编号
            folder_stats[folder] = folder_stats.get(folder, 0) + 1
        
        f.write("\n各文件夹文件分布:\n")
        for folder, count in sorted(folder_stats.items()):
            f.write(f"- 文件夹{folder}: {count}个文件\n")
        
        # 写入所有文件的详细信息
        f.write("\n2. 文件列表\n")
        f.write("-" * 40 + "\n")
        for file_id in sorted(file_dict.keys()):
            f.write(f"{file_id}\n")

        # 写入分组结果
        f.write("\n3. 同缆分组结果\n")
        f.write("-" * 40 + "\n")
        
        # 处理分组结果
        group_section = False
        group_results = []
        for result in results:
            if "=== 同缆分组结果" in result:
                group_section = True
                continue
            elif "=== 详细距离结果 ===" in result:
                group_section = False
                continue
            elif group_section and result.strip():
                group_results.append(result)
        
        # 写入分组结果
        if group_results:
            for result in group_results:
                f.write(result + "\n")
        else:
            f.write("未发现同缆分组\n")
        
        # 写入详细距离结果
        f.write("\n4. 详细距离分析\n")
        f.write("-" * 40 + "\n")
        
        # 处理距离结果
        distance_section = False
        distance_results = []
        for result in results:
            if "=== 详细距离结果 ===" in result:
                distance_section = True
                continue
            elif distance_section and result.strip():
                distance_results.append(result)
        
        # 写入距离结果
        if distance_results:
            for result in distance_results:
                f.write(result + "\n")
        else:
            f.write("未发现距离分析结果\n")
        
        # 写入时间戳
        f.write(f"\n\n报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n")
    
    print(f"\n分析报告已保存至: {save_path}")

if __name__ == "__main__":
    import time
    
    folder_path = "C:/Users/<USER>/Desktop/干线测试" 
    file_data = process_folder(folder_path)

    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        # 绘制所有曲线并保存
        plot_all_curves(file_data, save_path='otdr_curves_comparison.png')
        
        # 训练新模型
        print("\n开始训练新模型...")
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 准备训练数据
        curves_pairs, labels = prepare_training_data(file_data)
        dataset = OTDRDataset(curves_pairs, labels)
        train_loader = DataLoader(dataset, 
                                batch_size=32, 
                                shuffle=True,
                                collate_fn=collate_fn)
        
        # 初始化模型
        model = OTDRNet(input_size=2, hidden_size=128).to(device)
        criterion = nn.BCELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        # 训练模型
        train_model(model, train_loader, criterion, optimizer, device)
        
        # 使用训练好的模型进行预测
        print("\n开始进行神经网络分析...")
        results = neural_network_compare(file_data, model, device)
        
        # 在终端显示结果
        print("\n=== 分析结果 ===")
        for result in results:
            print(result)
        
        # 生成分析报告
        generate_analysis_report(file_dict=file_data, results=results)
        
        # 保存新训练的模型
        model_filename = f'otdr_model_{time.strftime("%Y%m%d_%H%M%S")}.pth'
        torch.save({
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
        }, model_filename)
        print(f"\n新模型已保存为: {model_filename}") 